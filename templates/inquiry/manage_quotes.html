{% extends "base.html" %}

{% block title %}供应商报价管理{% endblock %}

{% block extra_css %}
<style>
    .quote-card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin-bottom: 1rem;
        transition: box-shadow 0.2s;
    }
    
    .quote-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .quote-header {
        background-color: #f8f9fa;
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
        border-radius: 8px 8px 0 0;
    }
    
    .quote-body {
        padding: 1rem;
    }
    
    .quote-actions {
        padding: 0.5rem 1rem;
        border-top: 1px solid #dee2e6;
        background-color: #f8f9fa;
        border-radius: 0 0 8px 8px;
    }
    
    .has-orders {
        border-left: 4px solid #28a745;
    }
    
    .no-orders {
        border-left: 4px solid #6c757d;
    }
    
    .filter-section {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>供应商报价管理</h2>
                <button class="btn btn-primary" onclick="refreshQuotes()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>

            <!-- 过滤器 -->
            <div class="filter-section">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">产品名称</label>
                        <input type="text" class="form-control" id="filterProductName" placeholder="输入产品名称">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">供应商名称</label>
                        <input type="text" class="form-control" id="filterSupplierName" placeholder="输入供应商名称">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button class="btn btn-outline-primary" onclick="applyFilters()">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                            <button class="btn btn-outline-secondary ms-2" onclick="clearFilters()">
                                <i class="bi bi-x-circle"></i> 清除
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报价列表 -->
            <div id="quotesContainer">
                <!-- 报价卡片将在这里动态加载 -->
            </div>

            <!-- 分页 -->
            <nav aria-label="报价分页">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- 分页按钮将在这里动态生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteQuoteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这个供应商报价吗？</p>
                <div id="deleteQuoteDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDeleteQuote()">删除</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let currentQuoteId = null;
const deleteModal = new bootstrap.Modal(document.getElementById('deleteQuoteModal'));

// 页面加载时获取报价列表
document.addEventListener('DOMContentLoaded', function() {
    loadQuotes();
});

// 加载报价列表
async function loadQuotes(page = 1) {
    try {
        const productName = document.getElementById('filterProductName').value;
        const supplierName = document.getElementById('filterSupplierName').value;
        
        const params = new URLSearchParams({
            page: page,
            per_page: 20
        });
        
        if (productName) params.append('product_name', productName);
        if (supplierName) params.append('supplier_name', supplierName);
        
        const response = await fetch(`/inquiry/supplier-quotes?${params}`);
        const data = await response.json();
        
        if (response.ok) {
            renderQuotes(data.quotes);
            renderPagination(data);
            currentPage = page;
        } else {
            showToast('error', data.error || '加载失败');
        }
    } catch (error) {
        console.error('加载报价列表失败:', error);
        showToast('error', '加载失败，请重试');
    }
}

// 渲染报价列表
function renderQuotes(quotes) {
    const container = document.getElementById('quotesContainer');
    
    if (quotes.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-inbox display-1 text-muted"></i>
                <h4 class="text-muted mt-3">暂无报价记录</h4>
            </div>
        `;
        return;
    }
    
    container.innerHTML = quotes.map(quote => `
        <div class="quote-card ${quote.has_orders ? 'has-orders' : 'no-orders'}">
            <div class="quote-header">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="mb-1">${quote.product_name}</h5>
                        <small class="text-muted">${quote.brand || '无品牌'}</small>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <span class="badge ${quote.has_orders ? 'bg-success' : 'bg-secondary'}">
                            ${quote.has_orders ? '已下单' : '未下单'}
                        </span>
                    </div>
                </div>
            </div>
            <div class="quote-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>供应商:</strong><br>
                        ${quote.supplier_name}<br>
                        <small class="text-muted">${quote.supplier_contact || '无联系方式'}</small>
                    </div>
                    <div class="col-md-2">
                        <strong>数量:</strong><br>
                        ${quote.quantity}
                    </div>
                    <div class="col-md-2">
                        <strong>报价:</strong><br>
                        ¥${quote.price.toFixed(2)}
                    </div>
                    <div class="col-md-2">
                        <strong>我的价格:</strong><br>
                        ¥${quote.my_price.toFixed(2)}
                    </div>
                    <div class="col-md-3">
                        <strong>交货周期:</strong><br>
                        ${quote.delivery_days} 天<br>
                        <small class="text-muted">询价日期: ${new Date(quote.inquiry_date).toLocaleDateString()}</small>
                    </div>
                </div>
                ${quote.customer_name ? `
                <div class="row mt-2">
                    <div class="col-12">
                        <strong>客户:</strong> ${quote.customer_name}
                    </div>
                </div>
                ` : ''}
            </div>
            <div class="quote-actions">
                <button class="btn btn-sm btn-outline-danger ${quote.has_orders ? 'disabled' : ''}" 
                        onclick="showDeleteQuote(${quote.id}, '${quote.product_name}', '${quote.supplier_name}')"
                        ${quote.has_orders ? 'disabled title="该报价已有关联订单，无法删除"' : ''}>
                    <i class="bi bi-trash"></i> 删除
                </button>
            </div>
        </div>
    `).join('');
}

// 渲染分页
function renderPagination(data) {
    const pagination = document.getElementById('pagination');
    
    if (data.pages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHtml = '';
    
    // 上一页
    if (data.has_prev) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadQuotes(${data.current_page - 1})">上一页</a>
            </li>
        `;
    }
    
    // 页码
    for (let i = 1; i <= data.pages; i++) {
        if (i === data.current_page) {
            paginationHtml += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
        } else {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadQuotes(${i})">${i}</a></li>`;
        }
    }
    
    // 下一页
    if (data.has_next) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadQuotes(${data.current_page + 1})">下一页</a>
            </li>
        `;
    }
    
    pagination.innerHTML = paginationHtml;
}

// 显示删除确认对话框
function showDeleteQuote(quoteId, productName, supplierName) {
    currentQuoteId = quoteId;
    document.getElementById('deleteQuoteDetails').innerHTML = `
        <strong>产品:</strong> ${productName}<br>
        <strong>供应商:</strong> ${supplierName}
    `;
    deleteModal.show();
}

// 确认删除报价
async function confirmDeleteQuote() {
    if (!currentQuoteId) return;
    
    try {
        const response = await fetch(`/inquiry/supplier-quote/${currentQuoteId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showToast('success', '报价删除成功');
            deleteModal.hide();
            loadQuotes(currentPage); // 重新加载当前页
        } else {
            showToast('error', result.error || '删除失败');
        }
    } catch (error) {
        console.error('删除报价失败:', error);
        showToast('error', '删除失败，请重试');
    }
}

// 应用过滤器
function applyFilters() {
    loadQuotes(1);
}

// 清除过滤器
function clearFilters() {
    document.getElementById('filterProductName').value = '';
    document.getElementById('filterSupplierName').value = '';
    loadQuotes(1);
}

// 刷新报价列表
function refreshQuotes() {
    loadQuotes(currentPage);
}

// Toast 提示函数
function showToast(type, message) {
    // 这里可以使用现有的 toast 函数，或者简单的 alert
    if (typeof window.showToast === 'function') {
        window.showToast(type, message);
    } else {
        alert(message);
    }
}
</script>
{% endblock %}
