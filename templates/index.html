{% extends "base.html" %}

{% block title %}系统主页{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1 class="text-center mb-5 main-title">系统控制中心</h1>
    
    <div class="row justify-content-center">
        {% for row in systems %}
        <div class="row mb-4 justify-content-center">
            {% for system in row %}
            <div class="col-md-{{ 12 // row|length }} mb-4">
                <a href="{{ system.url }}" class="card h-100 text-decoration-none system-card {{ system.color_class }}">
                    <div class="card-body d-flex flex-column align-items-center justify-content-center">
                        <div class="icon-wrapper mb-3">
                            {% if 'user-management' in system.url %}
                            <i class="bi bi-people-fill"></i>
                            {% elif 'audit' in system.url %}
                            <i class="bi bi-shield-check"></i>
                            {% elif 'customer' in system.url %}
                            <i class="bi bi-person-vcard-fill"></i>
                            {% elif 'inquiry' in system.url %}
                            <i class="bi bi-chat-right-quote-fill"></i>
                            {% elif 'order' in system.url %}
                            <i class="bi bi-cart-check-fill"></i>
                            {% elif 'inventory' in system.url %}
                            <i class="bi bi-box-seam-fill"></i>
                            {% elif 'outbound' in system.url %}
                            <i class="bi bi-box-arrow-right"></i>
                            {% else %}
                            <i class="bi bi-grid-1x2-fill"></i>
                            {% endif %}
                        </div>
                        <h5 class="card-title mb-2">{{ system.name }}</h5>
                        <div class="card-action">
                            <span class="action-text">点击进入系统</span>
                            <i class="bi bi-arrow-right-short"></i>
                        </div>
                    </div>
                </a>
            </div>
            {% endfor %}
        </div>
        {% endfor %}
    </div>
</div>

<style>
@import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css");

:root {
    --primary-gradient: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
    --data-gradient: linear-gradient(135deg, #3a7bd5 0%, #00d2ff 100%);
    --inquiry-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --order-gradient: linear-gradient(135deg, #2193b0 0%, #6dd5ed 100%);
    --audit-gradient: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
    
    --card-shadow: 0 10px 20px rgba(0,0,0,0.1);
    --hover-shadow: 0 15px 30px rgba(0,0,0,0.15);
    --text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.main-title {
    color: #2c3e50;
    font-weight: 700;
    letter-spacing: 1px;
    text-shadow: var(--text-shadow);
}

.system-card {
    border: none;
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
    box-shadow: var(--card-shadow);
    min-height: 200px;
    position: relative;
    overflow: hidden;
}

.system-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--primary-gradient);
    transition: height 0.3s ease;
}

.card-body {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.icon-wrapper {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient);
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.icon-wrapper i {
    font-size: 1.5rem;
    color: white;
}

.card-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.25rem;
    text-align: center;
    margin: 0;
    transition: transform 0.3s ease;
}

.card-action {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.card-action i {
    font-size: 1.2rem;
    margin-left: 4px;
    transition: transform 0.3s ease;
}

/* 卡片颜色样式 */
.system-card.primary-system::before { background: var(--primary-gradient); }
.system-card.data-system::before { background: var(--data-gradient); }
.system-card.inquiry-system::before { background: var(--inquiry-gradient); }
.system-card.order-system::before { background: var(--order-gradient); }
.system-card.audit-system::before { background: var(--audit-gradient); }

.primary-system .icon-wrapper { background: var(--primary-gradient); }
.data-system .icon-wrapper { background: var(--data-gradient); }
.inquiry-system .icon-wrapper { background: var(--inquiry-gradient); }
.order-system .icon-wrapper { background: var(--order-gradient); }
.audit-system .icon-wrapper { background: var(--audit-gradient); }

/* 悬浮效果 */
.system-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.system-card:hover::before {
    height: 100%;
    opacity: 0.1;
}

.system-card:hover .card-title {
    transform: translateY(-2px);
}

.system-card:hover .icon-wrapper {
    transform: scale(1.1);
}

.system-card:hover .card-action {
    color: #4b6cb7;
}

.system-card:hover .card-action i {
    transform: translateX(4px);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .system-card {
        min-height: 180px;
    }
    
    .icon-wrapper {
        width: 50px;
        height: 50px;
    }
    
    .card-title {
        font-size: 1.1rem;
    }
}
</style>
{% endblock %}