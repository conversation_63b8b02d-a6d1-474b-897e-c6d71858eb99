{% extends 'auth/base.html' %}

{% block title %}注册{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">用户注册</h4>
            </div>
            <div class="card-body">
                <form id="register-form" novalidate>
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="password-strength"></div>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">确认密码</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="alert alert-danger d-none" id="register-error"></div>
                    
                    <button type="submit" class="btn btn-primary w-100" id="register-btn">
                        注册
                    </button>
                </form>
                
                <div class="mt-3 text-center">
                    <a href="{{ url_for('auth.login') }}">已有账号？立即登录</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='auth/js/register.js') }}"></script>
{% endblock %} 