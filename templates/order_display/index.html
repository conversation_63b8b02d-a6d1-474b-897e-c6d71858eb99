{% extends "base.html" %}

{% block title %}产品下单展示系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>产品下单展示系统</h2>
        <a href="/" class="btn btn-outline-primary">
            <i class="bi bi-house-door"></i> 返回首页
        </a>
    </div>
    
    <div class="card mb-4">
        <div class="card-body">
            <form id="searchForm" class="row g-3">
                <div class="col-md-4">
                    <label for="customerSelect" class="form-label">选择客户</label>
                    <select id="customerSelect" class="form-select" required>
                        <option value="">选择客户...</option>
                    </select>
                </div>
                
                <div class="col-md-4">
                    <label for="dateRange" class="form-label">日期区间</label>
                    <div class="input-group">
                        <input type="date" id="startDate" class="form-control" required>
                        <span class="input-group-text">至</span>
                        <input type="date" id="endDate" class="form-control" required>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <label class="form-label">快捷选择</label>
                    <div class="btn-group w-100">
                        <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('today')">今天</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('7days')">近7天</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('14days')">近14天</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('30days')">近1个月</button>
                    </div>
                </div>
                
                <div class="col-12">
                    <div class="d-flex justify-content-end gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 查询
                        </button>
                        <button type="button" class="btn btn-success" onclick="downloadExcel()" id="downloadBtn">
                            <i class="bi bi-download"></i> 
                            <span id="downloadBtnText">下载Invoice</span>
                            <span id="downloadSpinner" class="spinner-border spinner-border-sm ms-1" role="status" style="display: none;"></span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-bordered">
            <thead>
                <tr>
                    <th>订单日期</th>
                    <th>产品名称</th>
                    <th>品牌</th>
                    <th>数量</th>
                    <th>供应商</th>
                    <th>供应商价格</th>
                    <th>我的价格</th>
                    <th>预计交货日期</th>
                    <th>状态</th>
                    <th>备注</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="orderTableBody">
                <!-- 数据将通过JavaScript动态填充 -->
            </tbody>
        </table>
    </div>
</div>

<!-- 预计交货日期更新对话框 -->
<div class="modal fade" id="deliveryDateModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">更新预计交货日期</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="newDeliveryDate" class="form-label">新预计交货日期</label>
                    <input type="date" id="newDeliveryDate" class="form-control">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateDeliveryDate()">确认更新</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
<style>
    .table {
        background-color: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
    }
    .table thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        color: #2c3e50;
        font-weight: 600;
    }
    .table td {
        vertical-align: middle;
    }
    
    /* 状态基础样式 */
    .form-select-sm {
        padding: 0.35rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.85em;
        font-weight: 500;
    }
    
    /* 待处理 - 橙色系 */
    select.form-select.status-pending {
        color: #d97706 !important;
        background-color: #fffbeb !important;
        border-color: #fcd34d !important;
    }
    
    /* 已确认 - 商务蓝 */
    select.form-select.status-confirmed {
        color: #2563eb !important;
        background-color: #dbeafe !important;
        border-color: #93c5fd !important;
    }
    
    /* 已发货 - 活力紫 */
    select.form-select.status-shipped {
        color: #7c3aed !important;
        background-color: #ede9fe !important;
        border-color: #c4b5fd !important;
    }
    
    /* 已完成 - 绿色系 */
    select.form-select.status-completed {
        color: #15803d !important;
        background-color: #dcfce7 !important;
        border-color: #86efac !important;
    }
    
    /* 已取消 - 红色系 */
    select.form-select.status-cancelled {
        color: #dc2626 !important;
        background-color: #fee2e2 !important;
        border-color: #fca5a5 !important;
    }
    
    /* 下拉框选项样式 */
    select.form-select option.status-pending {
        color: #d97706;
        background-color: #fffbeb;
    }
    
    select.form-select option.status-confirmed {
        color: #2563eb;
        background-color: #dbeafe;
    }
    
    select.form-select option.status-shipped {
        color: #7c3aed;
        background-color: #ede9fe;
    }
    
    select.form-select option.status-completed {
        color: #15803d;
        background-color: #dcfce7;
    }
    
    select.form-select option.status-cancelled {
        color: #dc2626;
        background-color: #fee2e2;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
<script>
let currentOrderId = null;
const deliveryDateModal = new bootstrap.Modal(document.getElementById('deliveryDateModal'));

// 加载客户列表
async function loadCustomers() {
    try {
        const response = await fetch('/order-display/customers');
        const customers = await response.json();
        const select = document.getElementById('customerSelect');
        
        customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            select.appendChild(option);
        });
        
        // 如果有客户，自动选择第一个并加载数据
        if (customers.length > 0) {
            select.value = customers[0].id;
            loadOrders();
        }
        
        // 添加change事件监听器
        select.addEventListener('change', loadOrders);
    } catch (error) {
        console.error('加载客户列表失败:', error);
        showToast('error', '加载客户列表失败');
    }
}

// 设置日期范围的函数
function setDateRange(range) {
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const today = moment();
    
    // 设置结束日期为今天
    endDate.value = today.format('YYYY-MM-DD');
    
    // 根据选择设置开始日期
    switch(range) {
        case 'today':
            startDate.value = today.format('YYYY-MM-DD');
            break;
        case '7days':
            startDate.value = today.subtract(6, 'days').format('YYYY-MM-DD');
            break;
        case '14days':
            startDate.value = today.subtract(13, 'days').format('YYYY-MM-DD');
            break;
        case '30days':
            startDate.value = today.subtract(29, 'days').format('YYYY-MM-DD');
            break;
    }
}

// 页面加载时设置默认日期为今天
document.addEventListener('DOMContentLoaded', function() {
    loadCustomers();
    setDateRange('today');
    
    // 添加表单提交事件监听
    document.getElementById('searchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        loadOrders();
    });
});

// 加载订单记录
async function loadOrders() {
    const customerId = document.getElementById('customerSelect').value;
    const startDateLocal = document.getElementById('startDate').value;
    const endDateLocal = document.getElementById('endDate').value;
    
    if (!customerId || !startDateLocal || !endDateLocal) {
        showToast('warning', '请选择客户和日期范围');
        return;
    }
    
    // 直接使用本地时间，不进行时区转换
    const startDate = moment(startDateLocal).startOf('day').format('YYYY-MM-DD');
    const endDate = moment(endDateLocal).endOf('day').format('YYYY-MM-DD');
    
    try {
        const response = await fetch(`/order-display/orders?customer_id=${customerId}&start_date=${startDate}&end_date=${endDate}`);
        const orders = await response.json();
        
        // 按订单日期升序排序（从早到晚）
        orders.sort((a, b) => new Date(a.order_date) - new Date(b.order_date));
        
        const tbody = document.getElementById('orderTableBody');
        tbody.innerHTML = '';
        
        if (orders.length === 0) {
            tbody.innerHTML = '<tr><td colspan="11" class="text-center">暂无数据</td></tr>';
            return;
        }
        
        orders.forEach(order => {
            const tr = document.createElement('tr');
            const localOrderDate = moment(order.order_date);
            const localDeliveryDate = order.expected_delivery_date ? moment(order.expected_delivery_date) : null;
            
            tr.innerHTML = `
                <td>${localOrderDate.format('YYYY-MM-DD HH:mm:ss')}</td>
                <td>${order.product_name || '-'}</td>
                <td>${order.brand || '-'}</td>
                <td>${order.quantity || 0}</td>
                <td>${order.supplier_name || '-'}</td>
                <td>¥${order.price ? order.price.toFixed(2) : '0.00'}</td>
                <td>¥${order.my_price ? order.my_price.toFixed(2) : '0.00'}</td>
                <td>${localDeliveryDate ? localDeliveryDate.format('YYYY-MM-DD') : '-'}</td>
                <td>
                    <select class="form-select form-select-sm status-${order.status}" 
                            onchange="updateOrderStatus(${order.id}, this.value, this)">
                        <option value="pending" class="status-pending" ${order.status === 'pending' ? 'selected' : ''}>待处理</option>
                        <option value="confirmed" class="status-confirmed" ${order.status === 'confirmed' ? 'selected' : ''}>已确认</option>
                        <option value="shipped" class="status-shipped" ${order.status === 'shipped' ? 'selected' : ''}>已发货</option>
                        <option value="completed" class="status-completed" ${order.status === 'completed' ? 'selected' : ''}>已完成</option>
                        <option value="cancelled" class="status-cancelled" ${order.status === 'cancelled' ? 'selected' : ''}>已取消</option>
                    </select>
                </td>
                <td>${order.notes || '-'}</td>
                <td>
                    <button class="btn btn-outline-primary btn-sm" onclick="showDeliveryDateDialog(${order.id}, '${localDeliveryDate ? localDeliveryDate.format('YYYY-MM-DD') : ''}')" title="更新交货日期">
                        <i class="bi bi-calendar"></i> 更新日期
                    </button>
                </td>
            `;
            tbody.appendChild(tr);
            
            // 更新状态选择器的样式
            const statusSelect = tr.querySelector('select[onchange*="updateOrderStatus"]');
            updateStatusSelectStyle(statusSelect, order.status);
        });
    } catch (error) {
        console.error('加载订单记录失败:', error);
        showToast('error', '加载失败，请重试');
    }
}

// 更新状态选择器样式
function updateStatusSelectStyle(select, status) {
    select.classList.remove('status-pending', 'status-confirmed', 'status-shipped', 'status-completed', 'status-cancelled');
    select.classList.add(`status-${status}`);
}

// 更新订单状态
async function updateOrderStatus(orderId, newStatus, selectElement) {
    const originalStatus = selectElement.getAttribute('data-original-status');
    
    try {
        const response = await fetch(`/order-display/orders/${orderId}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: newStatus,
                notes: ''
            })
        });
        
        const result = await response.json();
        if (response.ok) {
            showToast('success', '状态更新成功');
            updateStatusSelectStyle(selectElement, newStatus);
            selectElement.setAttribute('data-original-status', newStatus);
        } else {
            showToast('error', result.error || '状态更新失败');
            // 恢复原始状态
            selectElement.value = originalStatus;
            updateStatusSelectStyle(selectElement, originalStatus);
        }
    } catch (error) {
        console.error('更新状态失败:', error);
        showToast('error', '更新状态失败，请重试');
        // 恢复原始状态
        selectElement.value = originalStatus;
        updateStatusSelectStyle(selectElement, originalStatus);
    }
}

// 下载 Excel
async function downloadExcel() {
    const customerId = document.getElementById('customerSelect').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    if (!customerId || !startDate || !endDate) {
        showToast('warning', '请选择客户和日期范围');
        return;
    }
    
    // 显示加载状态
    const downloadBtn = document.getElementById('downloadBtn');
    const downloadBtnText = document.getElementById('downloadBtnText');
    const downloadSpinner = document.getElementById('downloadSpinner');
    
    downloadBtn.disabled = true;
    downloadBtnText.textContent = '正在下载...';
    downloadSpinner.style.display = 'inline-block';
    
    try {
        // 获取汇率
        const rateResponse = await fetch('/forex/rate');
        const rateData = await rateResponse.json();
        let exchangeRate = -1;
        
        if (rateResponse.ok && rateData.rate) {
            exchangeRate = rateData.rate;
            showToast('success', `当前汇率: ${rateData.rate} (${rateData.timestamp})`);
        } else {
            showToast('warning', '实时汇率获取失败，将使用默认值0');
        }
        const response = await fetch(`/order-display/download?customer_id=${customerId}&start_date=${startDate}&end_date=${endDate}&exchange_rate=${exchangeRate}`);
        
        if (!response.ok) {
            throw new Error('下载失败');
        }
        
        // 创建一个blob链接并触发下载
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `订单记录_${startDate}_${endDate}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        showToast('success', '下载成功');
    } catch (error) {
        console.error('下载失败:', error);
        showToast('error', '下载失败，请重试');
    } finally {
        // 恢复按钮状态
        downloadBtn.disabled = false;
        downloadBtnText.textContent = '下载Excel';
        downloadSpinner.style.display = 'none';
    }
}

// 显示日期更新对话框
function showDeliveryDateDialog(orderId, currentDate) {
    currentOrderId = orderId;
    const dateInput = document.getElementById('newDeliveryDate');
    if (currentDate) {
        dateInput.value = currentDate;
    } else {
        dateInput.value = moment().format('YYYY-MM-DD');
    }
    deliveryDateModal.show();
}

// 更新预计交货日期
async function updateDeliveryDate() {
    const newDate = document.getElementById('newDeliveryDate').value;
    
    if (!newDate) {
        showToast('warning', '请选择新的预计交货日期');
        return;
    }
    
    try {
        const response = await fetch(`/order-display/orders/${currentOrderId}/delivery-date`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                expected_delivery_date: newDate
            })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showToast('success', result.message);
            deliveryDateModal.hide();
            loadOrders(); // 重新加载订单列表
        } else {
            showToast('error', result.error || '更新失败');
        }
    } catch (error) {
        console.error('更新预计交货日期失败:', error);
        showToast('error', '更新失败，请重试');
    }
}

// 格式化日期时间
function formatDateTime(dateStr) {
    if (!dateStr) return '';
    return moment(dateStr).format('YYYY-MM-DD HH:mm');
}

// 格式化日期
function formatDate(dateStr) {
    if (!dateStr) return '-';
    return moment(dateStr).format('YYYY-MM-DD');
}
</script>
{% endblock %} 