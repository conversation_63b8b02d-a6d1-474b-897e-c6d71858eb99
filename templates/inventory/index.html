{% extends "base.html" %}

{% block title %}出入库管理系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>出入库管理系统</h2>
        <a href="/" class="btn btn-outline-primary">
            <i class="bi bi-house-door"></i> 返回首页
        </a>
    </div>
    
    <div class="card mb-4">
        <div class="card-body">
            <form id="searchForm" class="row g-3">
                <div class="col-md-4">
                    <label for="customerSelect" class="form-label">选择客户</label>
                    <select id="customerSelect" class="form-select" required>
                        <option value="">选择客户...</option>
                    </select>
                </div>
                
                <div class="col-md-4">
                    <label for="dateRange" class="form-label">日期区间</label>
                    <div class="input-group">
                        <input type="date" id="startDate" class="form-control" required>
                        <span class="input-group-text">至</span>
                        <input type="date" id="endDate" class="form-control" required>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <label class="form-label">快捷选择</label>
                    <div class="btn-group w-100">
                        <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('today')">今天</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('7days')">近7天</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('14days')">近14天</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('30days')">近1个月</button>
                    </div>
                </div>
                
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div id="downloadButtonsContainer">
                            <!-- 下载按钮将通过 JavaScript 动态添加 -->
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <div class="table-responsive">
        <table class="table table-striped table-bordered">
            <thead>
                <tr>
                    <th>下单时间</th>
                    <th>产品名称</th>
                    <th>数量</th>
                    <th>到货时间</th>
                    <th>货运选择</th>
                    <th>出货时间</th>
                    <th>供应商</th>
                    <th>产品序列码</th>
                    <th>订单状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="inventoryTableBody">
                <tr>
                    <td colspan="9" class="text-center">暂无数据</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
<style>
    .table {
        background-color: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
    }
    .table thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        color: #2c3e50;
        font-weight: 600;
        white-space: nowrap;
    }
    .table td {
        vertical-align: middle;
    }
    .form-control-sm, .form-select-sm {
        height: calc(1.5em + 0.5rem + 2px);
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 0.2rem;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 0.2rem;
    }
    
    /* 订单状态颜色样式 */
    /* 待处理 - 橙色系 */
    .status-pending {
        color: #d97706 !important;
        background-color: #fffbeb !important;
        border-color: #fcd34d !important;
    }
    
    /* 已确认 - 商务蓝 */
    .status-confirmed {
        color: #2563eb !important;
        background-color: #dbeafe !important;
        border-color: #93c5fd !important;
    }
    
    /* 已发货 - 活力紫 */
    .status-shipped {
        color: #7c3aed !important;
        background-color: #ede9fe !important;
        border-color: #c4b5fd !important;
    }
    
    /* 已完成 - 绿色系 */
    .status-completed {
        color: #15803d !important;
        background-color: #dcfce7 !important;
        border-color: #86efac !important;
    }
    
    /* 已取消 - 红色系 */
    .status-cancelled {
        color: #dc3545 !important;
        background-color: #fee2e2 !important;
        border-color: #fca5a5 !important;
    }
</style>
{% endblock %}

<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
// 设置moment.js的默认时区为用户本地时区
moment.tz.setDefault(moment.tz.guess());

// 加载客户列表
async function loadCustomers() {
    try {
        const response = await fetch('/inventory/customers');
        const customers = await response.json();
        const select = document.getElementById('customerSelect');
        
        customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            select.appendChild(option);
        });
        
        // 如果有客户，自动选择第一个并加载数据
        if (customers.length > 0) {
            select.value = customers[0].id;
            loadInventory();
        }
        
        // 添加change事件监听器
        select.addEventListener('change', loadInventory);
    } catch (error) {
        console.error('加载客户列表失败:', error);
        showToast('error', '加载客户列表失败');
    }
}

// 设置日期范围
function setDateRange(range) {
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const today = moment();
    
    // 设置结束日期为今天
    endDate.value = today.format('YYYY-MM-DD');
    
    // 根据选择设置开始日期
    switch(range) {
        case 'today':
            startDate.value = today.format('YYYY-MM-DD');
            break;
        case '7days':
            startDate.value = today.subtract(6, 'days').format('YYYY-MM-DD');
            break;
        case '14days':
            startDate.value = today.subtract(13, 'days').format('YYYY-MM-DD');
            break;
        case '30days':
            startDate.value = today.subtract(29, 'days').format('YYYY-MM-DD');
            break;
    }
}

// 加载库存记录
async function loadInventory() {
    const customerId = document.getElementById('customerSelect').value;
    const startDateLocal = document.getElementById('startDate').value;
    const endDateLocal = document.getElementById('endDate').value;
    
    if (!customerId || !startDateLocal || !endDateLocal) {
        showToast('warning', '请选择客户和日期范围');
        return;
    }
    
    // 直接使用本地时间，不进行时区转换
    const startDate = moment(startDateLocal).startOf('day').format('YYYY-MM-DD HH:mm:ss');
    const endDate = moment(endDateLocal).endOf('day').format('YYYY-MM-DD HH:mm:ss');
    
    try {
        const response = await fetch(`/inventory/orders?customer_id=${customerId}&start_date=${startDate}&end_date=${endDate}`);
        const data = await response.json();

        // 按订单日期升序排序
        data.orders.sort((a, b) => new Date(a.order_date) - new Date(b.order_date));
        
        // 检查是否有海运和空运记录
        const hasSeaShipment = data.orders?.some(record => 
            record.shipping_method === 'sea' && record.shipping_date);
        const hasAirShipment = data.orders?.some(record => 
            record.shipping_method === 'air' && record.shipping_date);
            
        // 获取按钮容器
        const buttonContainer = document.getElementById('downloadButtonsContainer');
        buttonContainer.innerHTML = ''; // 清空现有按钮
        
        // 如果有海运记录，添加海运下载按钮
        if (hasSeaShipment) {
            const seaButton = document.createElement('button');
            seaButton.type = 'button';
            seaButton.className = 'btn btn-success me-2';
            seaButton.onclick = () => downloadExcel('sea');
            seaButton.innerHTML = '<i class="bi bi-download"></i> 下载海运Excel';
            buttonContainer.appendChild(seaButton);
        }
        
        // 如果有空运记录，添加空运下载按钮
        if (hasAirShipment) {
            const airButton = document.createElement('button');
            airButton.type = 'button';
            airButton.className = 'btn btn-info';
            airButton.onclick = () => downloadExcel('air');
            airButton.innerHTML = '<i class="bi bi-download"></i> 下载空运Excel';
            buttonContainer.appendChild(airButton);
        }
        
        const tbody = document.getElementById('inventoryTableBody');
        tbody.innerHTML = '';
        
        if (!data.orders || data.orders.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center">暂无数据</td></tr>';
            return;
        }
        
        // 过滤出选定日期范围内的记录
        const filteredOrders = data.orders.filter(record => {
            // 检查 order_date 是否有效
            if (!record.order_date) {
                console.warn('记录缺少订单时间:', record);
                return false;
            }
            
            try {
                const orderTime = moment(record.order_date);
                const startTime = moment(startDateLocal).startOf('day');
                const endTime = moment(endDateLocal).endOf('day');
                
                // 验证时间对象是否有效
                if (!orderTime.isValid() || !startTime.isValid() || !endTime.isValid()) {
                    console.warn('无效的时间对象:', {
                        orderTime: orderTime.format(),
                        startTime: startTime.format(),
                        endTime: endTime.format()
                    });
                    return false;
                }
                
                return orderTime.isBetween(startTime, endTime, null, '[]');
            } catch (error) {
                console.error('时间转换错误:', error);
                return false;
            }
        });
        
        if (filteredOrders.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center">暂无数据</td></tr>';
            return;
        }
        
        filteredOrders.forEach(record => {
            const tr = document.createElement('tr');
            tr.setAttribute('data-id', record.inventory_id || '');  // 使用库存ID，如果没有则为空字符串
            try {
                // 直接使用订单时间，不进行时区转换
                const orderDate = moment(record.order_date);
                const shippingDate = record.shipping_date ? moment(record.shipping_date) : null;
                
                if (!orderDate.isValid()) {
                    console.warn('无效的订单时间:', record.order_date);
                    return;
                }
                
                // 如果没有库存记录，禁用输入控件
                const isDisabled = !record.inventory_id ? 'disabled' : '';
                
                tr.innerHTML = `
                    <td>${orderDate.format('YYYY-MM-DD HH:mm:ss')}</td>
                    <td>${record.product_name || '-'}</td>
                    <td>${record.quantity || 0}</td>
                    <td>
                        <input type="date" class="form-control form-control-sm" 
                               name="arrival_date"
                               ${isDisabled}
                               value="${record.arrival_date || ''}"
                               onchange="updateInventory(${record.inventory_id}, 'arrival_date', this.value)">
                    </td>
                    <td>
                        <select class="form-select form-select-sm" 
                                name="shipping_method"
                                ${isDisabled}
                                onchange="updateInventory(${record.inventory_id}, 'shipping_method', this.value)">
                            <option value="">请选择...</option>
                            <option value="sea" ${record.shipping_method === 'sea' ? 'selected' : ''}>海运</option>
                            <option value="air" ${record.shipping_method === 'air' ? 'selected' : ''}>空运</option>
                        </select>
                    </td>
                    <td>
                        <input type="date" class="form-control form-control-sm" 
                               name="shipping_date"
                               ${isDisabled}
                               value="${shippingDate && shippingDate.isValid() ? shippingDate.format('YYYY-MM-DD') : ''}"
                               onchange="updateInventory(${record.inventory_id}, 'shipping_date', this.value)">
                    </td>
                    <td>${record.supplier_name || '-'}</td>
                    <td>
                        <input type="text" class="form-control form-control-sm" 
                               name="serial_number"
                               ${isDisabled}
                               value="${record.serial_number || ''}"
                               onchange="updateInventory(${record.inventory_id}, 'serial_number', this.value)">
                    </td>
                    <td>
                        <select class="form-select form-select-sm status-${record.status}" 
                                onchange="updateOrderStatus(${record.id}, this.value)">
                            <option value="pending" class="status-pending" ${record.status === 'pending' ? 'selected' : ''}>待处理</option>
                            <option value="confirmed" class="status-confirmed" ${record.status === 'confirmed' ? 'selected' : ''}>已确认</option>
                            <option value="shipped" class="status-shipped" ${record.status === 'shipped' ? 'selected' : ''}>已发货</option>
                            <option value="completed" class="status-completed" ${record.status === 'completed' ? 'selected' : ''}>已完成</option>
                            <option value="cancelled" class="status-cancelled" ${record.status === 'cancelled' ? 'selected' : ''}>已取消</option>
                        </select>
                    </td>
                    <td>
                        <button class="btn btn-outline-primary btn-sm" onclick="saveChanges(${record.id})">
                            <i class="bi bi-save"></i> 保存
                        </button>
                    </td>
                `;
                
                tbody.appendChild(tr);
                
                // 初始化状态选择器的样式
                const statusSelect = tr.querySelector('select[onchange*="updateOrderStatus"]');
                updateStatusSelectStyle(statusSelect, record.status);
            } catch (error) {
                console.error('渲染记录时出错:', error);
                tr.innerHTML = '<td colspan="9" class="text-center text-danger">数据格式错误</td>';
            }
        });
    } catch (error) {
        console.error('加载库存记录失败:', error);
        showToast('error', '加载失败，请重试');
    }
}

// 更新库存记录
async function updateInventory(id, field, value) {
    try {
        // 检查ID是否有效
        if (!id) {
            showToast('error', '无效的库存记录');
            return;
        }

        // 格式化日期字段
        if (field === 'arrival_date' || field === 'shipping_date') {
            // 如果日期为空，发送null
            value = value ? value : null;
        }

        const response = await fetch(`/inventory/update/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                [field]: value
            })
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '更新失败');
        }
        
        const result = await response.json();
        
        if (result.message === '更新成功') {
            // 更新本地数据
            const row = document.querySelector(`tr[data-id="${id}"]`);
            if (row) {
                // 更新到货时间
                if (field === 'arrival_date') {
                    const arrivalDateInput = row.querySelector('input[name="arrival_date"]');
                    if (arrivalDateInput) {
                        arrivalDateInput.value = result.inventory?.arrival_date || '';
                    }
                }
                
                // 更新货运方式
                if (field === 'shipping_method') {
                    const shippingSelect = row.querySelector('select[name="shipping_method"]');
                    if (shippingSelect) {
                        shippingSelect.value = result.inventory?.shipping_method || '';
                    }
                    updateDownloadButtons();
                }
                
                // 更新出货时间
                if (field === 'shipping_date') {
                    const shippingDateInput = row.querySelector('input[name="shipping_date"]');
                    if (shippingDateInput) {
                        shippingDateInput.value = result.inventory?.shipping_date || '';
                    }
                    updateDownloadButtons();
                }
                
                // 更新序列号
                if (field === 'serial_number') {
                    const serialInput = row.querySelector('input[name="serial_number"]');
                    if (serialInput) {
                        serialInput.value = result.inventory?.serial_number || '';
                    }
                }
            }
            
            showToast('success', '更新成功');
        } else {
            throw new Error(result.error || '更新失败');
        }
    } catch (error) {
        console.error('Update failed:', error);
        showToast('error', error.message || '更新失败，请重试');
    }
}

// 新增函数：更新下载按钮
async function updateDownloadButtons() {
    const customerId = document.getElementById('customerSelect').value;
    const startDateLocal = document.getElementById('startDate').value;
    const endDateLocal = document.getElementById('endDate').value;
    
    if (!customerId || !startDateLocal || !endDateLocal) {
        return;
    }
    
    const startDate = moment(startDateLocal).startOf('day').format('YYYY-MM-DD HH:mm:ss');
    const endDate = moment(endDateLocal).endOf('day').format('YYYY-MM-DD HH:mm:ss');
    
    try {
        const response = await fetch(`/inventory/orders?customer_id=${customerId}&start_date=${startDate}&end_date=${endDate}`);
        const data = await response.json();
        
        // 检查是否有海运和空运记录
        const hasSeaShipment = data.orders?.some(record => 
            record.shipping_method === 'sea' && record.shipping_date);
        const hasAirShipment = data.orders?.some(record => 
            record.shipping_method === 'air' && record.shipping_date);
            
        // 获取按钮容器
        const buttonContainer = document.getElementById('downloadButtonsContainer');
        buttonContainer.innerHTML = ''; // 清空现有按钮
        
        // 如果有海运记录，添加海运下载按钮
        if (hasSeaShipment) {
            const seaButton = document.createElement('button');
            seaButton.type = 'button';
            seaButton.className = 'btn btn-success me-2';
            seaButton.onclick = () => downloadExcel('sea');
            seaButton.innerHTML = '<i class="bi bi-download"></i> 下载海运Excel';
            buttonContainer.appendChild(seaButton);
        }
        
        // 如果有空运记录，添加空运下载按钮
        if (hasAirShipment) {
            const airButton = document.createElement('button');
            airButton.type = 'button';
            airButton.className = 'btn btn-info';
            airButton.onclick = () => downloadExcel('air');
            airButton.innerHTML = '<i class="bi bi-download"></i> 下载空运Excel';
            buttonContainer.appendChild(airButton);
        }
    } catch (error) {
        console.error('更新下载按钮失败:', error);
    }
}

// 更新状态选择器的样式
function updateStatusSelectStyle(select, status) {
    // 移除所有状态相关的类
    select.classList.remove('status-pending', 'status-confirmed', 'status-shipped', 'status-completed', 'status-cancelled');
    // 添加当前状态的类
    select.classList.add(`status-${status}`);
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadCustomers();
    setDateRange('today');
    
    // 添加表单提交事件监听
    document.getElementById('searchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        loadInventory();
    });
});

// 下载 Excel
async function downloadExcel(shippingMethod) {
    const customerId = document.getElementById('customerSelect').value;
    const startDateLocal = document.getElementById('startDate').value;
    const endDateLocal = document.getElementById('endDate').value;
    
    if (!customerId || !startDateLocal || !endDateLocal) {
        showToast('warning', '请选择客户和日期范围');
        return;
    }
    
    try {
        // 获取汇率
        const rateResponse = await fetch('/forex/rate');
        const rateData = await rateResponse.json();
        let exchangeRate = -1;
        
        if (rateResponse.ok && rateData.rate) {
            exchangeRate = rateData.rate;
            showToast('success', `当前汇率: ${rateData.rate} (${rateData.timestamp})`);
        } else {
            showToast('warning', '实时汇率获取失败，将使用默认值0');
        }

        // 验证日期格式
        const startDate = moment(startDateLocal).startOf('day').format('YYYY-MM-DD HH:mm:ss');
        const endDate = moment(endDateLocal).endOf('day').format('YYYY-MM-DD HH:mm:ss');

        if (!moment(startDate).isValid() || !moment(endDate).isValid()) {
            showToast('error', '日期格式无效');
            return;
        }
        
        // 先获取所有订单数据
        const ordersResponse = await fetch(`/inventory/orders?customer_id=${customerId}&start_date=${startDate}&end_date=${endDate}&exchange_rate=${exchangeRate}`);
        const data = await ordersResponse.json();
        
        if (!data.orders || data.orders.length === 0) {
            showToast('warning', '没有找到符合条件的记录');
            return;
        }
        
        // 获取所有不同的出货日期
        const shippingDates = [...new Set(data.orders
            .filter(order => order.shipping_method === shippingMethod && order.shipping_date)
            .map(order => order.shipping_date.split(' ')[0]))];  // 只取日期部分
            
        if (shippingDates.length === 0) {
            showToast('warning', '没有找到对应运输方式的出货记录');
            return;
        }
        
        // 显示正在下载的提示
        showToast('success', `开始下载${shippingDates.length}个文件...`);
        
        // 按顺序下载每个日期的Excel
        for (const shippingDate of shippingDates) {
            // 构建该日期的时间范围
            const dateStartTime = moment(shippingDate).startOf('day').format('YYYY-MM-DD HH:mm:ss');
            const dateEndTime = moment(shippingDate).endOf('day').format('YYYY-MM-DD HH:mm:ss');
            
            const response = await fetch(`/inventory/download?customer_id=${customerId}&start_date=${dateStartTime}&end_date=${dateEndTime}&shipping_method=${shippingMethod}&exchange_rate=${exchangeRate}`);
            
            if (!response.ok) {
                throw new Error(`下载${shippingDate}的文件失败`);
            }
            
            // 下载文件
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            // 使用运输方式和具体日期作为文件名
            const transportType = shippingMethod === 'sea' ? '海运' : '空运';
            a.download = `${transportType}-${shippingDate}.xlsx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            // 等待一小段时间再下载下一个文件，避免浏览器限制
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        showToast('success', '所有文件下载完成');
    } catch (error) {
        console.error('下载失败:', error);
        showToast('error', error.message || '下载失败，请重试');
    }
}

// 更新订单状态
async function updateOrderStatus(id, value) {
    try {
        const response = await fetch(`/order-display/orders/${id}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                status: value
            })
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '更新失败');
        }
        
        const result = await response.json();
        if (result.message === '更新成功') {
            const statusSelect = document.querySelector(`select[onchange*="updateOrderStatus(${id}"]`);
            if (statusSelect) {
                updateStatusSelectStyle(statusSelect, value);
            }
            showToast('success', '状态更新成功');
        } else {
            throw new Error(result.error || '更新失败');
        }
    } catch (error) {
        console.error('更新状态失败:', error);
        showToast('error', error.message || '更新失败，请重试');
        loadInventory(); // 只在出错时重新加载
    }
}
</script>
{% endblock %} 