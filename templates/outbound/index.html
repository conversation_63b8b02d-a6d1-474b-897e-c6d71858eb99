{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col">
            <h2>出库清单管理</h2>
        </div>
    </div>

    <!-- 客户选择 -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-end">
                <div class="col-md-4">
                    <label class="form-label">选择客户</label>
                    <select class="form-select" id="customer">
                        <option value="">选择客户...</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-primary" onclick="downloadOutbound()" id="downloadBtn" disabled>
                        <i class="bi bi-download"></i> 下载清单
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 加载客户列表
    loadCustomers();
    
    // 添加客户选择事件监听
    document.getElementById('customer').addEventListener('change', function() {
        document.getElementById('downloadBtn').disabled = !this.value;
    });
});

// 加载客户列表
async function loadCustomers() {
    try {
        const response = await fetch('/customers');
        const customers = await response.json();
        
        const select = document.getElementById('customer');
        select.innerHTML = '<option value="">选择客户...</option>';
        
        customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('加载客户列表失败:', error);
    }
}

// 下载出库清单
function downloadOutbound() {
    const customerId = document.getElementById('customer').value;
    if (!customerId) {
        alert('请先选择客户');
        return;
    }
    
    window.location.href = `/outbound/download/${customerId}`;
}
</script>
{% endblock %} 