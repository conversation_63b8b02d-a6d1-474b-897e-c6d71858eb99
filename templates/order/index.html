{% extends "base.html" %}

{% block title %}产品下单管理系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>产品下单管理系统</h2>
        <a href="/" class="btn btn-outline-primary">
            <i class="bi bi-house-door"></i> 返回首页
        </a>
    </div>

    <div class="card mb-4">
        <div class="card-body">
            <form id="searchForm" class="row g-3">
                <div class="col-md-4">
                    <label for="customerSelect" class="form-label">选择客户</label>
                    <select id="customerSelect" class="form-select" required>
                        <option value="">选择客户...</option>
                    </select>
                </div>

                <div class="col-md-4">
                    <label for="dateRange" class="form-label">日期区间</label>
                    <div class="input-group">
                        <input type="date" id="startDate" class="form-control" required>
                        <span class="input-group-text">至</span>
                        <input type="date" id="endDate" class="form-control" required>
                    </div>
                </div>

                <div class="col-md-4">
                    <label class="form-label">快捷选择</label>
                    <div class="btn-group w-100">
                        <button type="button" class="btn btn-outline-secondary"
                            onclick="setDateRange('today')">今天</button>
                        <button type="button" class="btn btn-outline-secondary"
                            onclick="setDateRange('7days')">近7天</button>
                        <button type="button" class="btn btn-outline-secondary"
                            onclick="setDateRange('14days')">近14天</button>
                        <button type="button" class="btn btn-outline-secondary"
                            onclick="setDateRange('30days')">近1个月</button>
                    </div>
                </div>

                <div class="col-12">
                    <div class="d-flex justify-content-end gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-bordered">
            <thead>
                <tr>
                    <th>日期</th>
                    <th>产品名称</th>
                    <th>数量</th>
                    <th>我的报价</th>
                    <th>预计交货天数</th>
                    <th>供应商报价</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="inquiryTableBody">
                <!-- 数据将通过JavaScript动态填充 -->
            </tbody>
        </table>
    </div>
</div>

<!-- 添加订单对话框 -->
<div class="modal fade" id="orderModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建订单</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">供应商</label>
                    <div id="selectedSupplier" class="form-control-plaintext"></div>
                </div>
                <div class="mb-3">
                    <label class="form-label">报价</label>
                    <div id="selectedPrice" class="form-control-plaintext"></div>
                </div>
                <!-- 新增数量输入 -->
                <div class="mb-3">
                    <label for="orderQuantity" class="form-label">下单数量</label>
                    <input type="number" id="orderQuantity" class="form-control" min="1" value="1" required>
                </div>
                <div class="mb-3">
                    <label for="orderNotes" class="form-label">备注</label>
                    <textarea id="orderNotes" class="form-control" rows="3" placeholder="请输入订单备注信息（可选）"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitOrder()">确认下单</button>
            </div>
        </div>
    </div>
</div>

<!-- 订单记录对话框 -->
<div class="modal fade" id="orderSummaryModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">订单记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>订单时间</th>
                                <th>供应商</th>
                                <th>数量</th>
                                <th>价格</th>
                                <th>交货天数</th>
                                <th>状态</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody id="orderRecordsBody">
                            <!-- 订单记录将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>
                <div id="noOrdersMessage" class="text-center p-3 d-none">
                    暂无订单记录
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 修改订单对话框 -->
<div class="modal fade" id="editOrderModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">修改订单</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="editOrderId">
                <div class="mb-3">
                    <label for="editOrderQuantity" class="form-label">下单数量</label>
                    <input type="number" id="editOrderQuantity" class="form-control" min="1" required>
                </div>
                <div class="mb-3">
                    <label for="editOrderNotes" class="form-label">备注</label>
                    <textarea id="editOrderNotes" class="form-control" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" onclick="deleteOrder()">删除订单</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateOrder()">保存修改</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
<style>
    .supplier-quote {
        margin-bottom: 8px;
        padding: 4px;
        border: 1px solid #eee;
        border-radius: 4px;
    }

    .supplier-quote:last-child {
        margin-bottom: 0;
    }

    .supplier-info {
        margin-bottom: 4px;
    }

    .supplier-name {
        font-weight: bold;
        margin-right: 8px;
    }

    .supplier-contact {
        color: #666;
    }

    .quote-details {
        display: flex;
        justify-content: space-between;
        color: #333;
    }

    .price {
        color: #e74c3c;
        font-weight: bold;
    }

    .delivery-days {
        color: #3498db;
    }

    .table {
        background-color: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
    }

    .table thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        color: #2c3e50;
        font-weight: 600;
    }

    .table td {
        vertical-align: middle;
    }

    .table th {
        white-space: nowrap;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone.min.js"></script>
<script>
    // 设置moment.js的默认时区为用户本地时区
    moment.tz.setDefault(moment.tz.guess());

    // 设置日期范围的函数
    function setDateRange(range) {
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');
        const today = moment();

        // 设置结束日期为今天
        endDate.value = today.format('YYYY-MM-DD');

        // 根据选择设置开始日期
        switch (range) {
            case 'today':
                startDate.value = today.format('YYYY-MM-DD');
                break;
            case '7days':
                startDate.value = today.subtract(6, 'days').format('YYYY-MM-DD');
                break;
            case '14days':
                startDate.value = today.subtract(13, 'days').format('YYYY-MM-DD');
                break;
            case '30days':
                startDate.value = today.subtract(29, 'days').format('YYYY-MM-DD');
                break;
        }
    }

    // 格式化日期
    function formatDate(dateStr) {
        if (!dateStr) return '-';
        return moment(dateStr).format('YYYY-MM-DD HH:mm:ss');
    }

    // 加载客户列表
    async function loadCustomers() {
        try {
            const response = await fetch('/order/customers');
            const customers = await response.json();
            const select = document.getElementById('customerSelect');

            customers.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.id;
                option.textContent = customer.name;
                select.appendChild(option);
            });
        } catch (error) {
            console.error('加载客户列表失败:', error);
            showToast('error', '加载客户列表失败');
        }
    }

    // 页面加载时设置默认日期为今天
    document.addEventListener('DOMContentLoaded', function () {
        loadCustomers();
        setDateRange('today');

        // 添加表单提交事件监听
        document.getElementById('searchForm').addEventListener('submit', function (e) {
            e.preventDefault();
            loadInquiries();
        });
    });

    // 加载询价记录
    async function loadInquiries() {
        const customerId = document.getElementById('customerSelect').value;
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;

        if (!customerId || !startDate || !endDate) {
            showToast('warning', '请选择客户和日期范围');
            return;
        }

        try {
            const response = await fetch(`/order/inquiries?customer_id=${customerId}&start_date=${startDate}&end_date=${endDate}`);
            const inquiries = await response.json();

            const tbody = document.getElementById('inquiryTableBody');
            tbody.innerHTML = '';

            if (inquiries.length === 0) {
                const tr = document.createElement('tr');
                tr.innerHTML = '<td colspan="7" class="text-center">暂无数据</td>';
                tbody.appendChild(tr);
                return;
            }

            inquiries.forEach(inquiry => {
                const tr = document.createElement('tr');
                const supplierQuotesHtml = inquiry.supplier_quotes.map(quote =>
                    generateSupplierQuoteHtml(quote, inquiry.id)
                ).join('');

                tr.innerHTML = `
                <td>${inquiry.inquiry_date ? moment(inquiry.inquiry_date).format('YYYY-MM-DD') : '-'}</td>
                <td>${inquiry.product_name || '-'}</td>
                <td>${inquiry.quantity || 0}</td>
                <td>¥${inquiry.my_price ? inquiry.my_price.toFixed(2) : '0.00'}</td>
                <td>${inquiry.expected_delivery_days || 0}天</td>
                <td>${supplierQuotesHtml}</td>
                <td>
                    <button class="btn btn-outline-primary btn-sm" onclick="showOrderSummary(${inquiry.id})" title="查看订单记录">
                        <i class="bi bi-list-ul"></i> 订单记录
                    </button>
                </td>
            `;

                tbody.appendChild(tr);
            });
        } catch (error) {
            console.error('加载询价记录失败:', error);
            showToast('error', '加载失败，请重试');
        }
    }

    // 修改供应商报价的HTML生成部分
    function generateSupplierQuoteHtml(quote, inquiryId) {
        return `
        <div class="supplier-quote">
            <div class="supplier-info">
                <span class="supplier-name">${quote.supplier_name || ''}</span>
                ${quote.supplier_contact ? `<span class="supplier-contact">(${quote.supplier_contact})</span>` : ''}
            </div>
            <div class="quote-details">
                <span class="price">¥${quote.price ? quote.price.toFixed(2) : '0.00'}</span>
                <span class="delivery-days">${quote.delivery_days || 0}天</span>
                ${quote.has_order ?
                `<div class="btn-group">
                        <button class="btn btn-secondary btn-sm" disabled>
                            <i class="bi bi-check-circle"></i> 已下单
                        </button>
                        <button class="btn btn-warning btn-sm" 
                            onclick="showEditOrderDialog(${quote.order_id}, ${quote.order_quantity}, ${inquiryId}, ${quote.id}, '${quote.supplier_name || ''}', ${quote.price || 0})"
                            title="修改订单">
                            <i class="bi bi-pencil"></i>
                        </button>
                    </div>` :
                `<button class="btn btn-primary btn-sm" 
                        onclick="showOrderDialog(${inquiryId}, ${quote.id}, '${quote.supplier_name || ''}', ${quote.price || 0})"
                        title="选择此供应商下单">
                        <i class="bi bi-plus-circle"></i> 选择下单
                    </button>`
            }
            </div>
        </div>
    `;
    }

    // 添加新的JavaScript函数
    let currentOrderData = null;
    const orderModal = new bootstrap.Modal(document.getElementById('orderModal'));

    function showOrderDialog(inquiryId, supplierQuoteId, supplierName, price) {
        currentOrderData = {
            inquiry_id: inquiryId,
            supplier_quote_id: supplierQuoteId
        };

        document.getElementById('selectedSupplier').textContent = supplierName || '-';
        document.getElementById('selectedPrice').textContent = `¥${price ? price.toFixed(2) : '0.00'}`;
        document.getElementById('orderQuantity').value = '1';
        document.getElementById('orderNotes').value = '';

        orderModal.show();
    }

    async function submitOrder() {
        if (!currentOrderData) {
            showToast('error', '订单数据无效');
            return;
        }

        // 获取提交按钮并防止重复提交
        const submitButton = document.querySelector('#orderModal button.btn-primary');
        if (submitButton.disabled) {
            console.log('Order submit button already disabled, preventing duplicate submission');
            return;
        }

        const originalText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';

        const quantity = document.getElementById('orderQuantity').value.trim();
        const notes = document.getElementById('orderNotes').value.trim();
        currentOrderData.quantity = quantity;
        currentOrderData.notes = notes;

        try {
            const response = await fetch('/order/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(currentOrderData)
            });

            const result = await response.json();
            if (response.ok) {
                showToast('success', '订单创建成功');
                orderModal.hide();
                loadInquiries(); // 刷新列表
            } else {
                showToast('error', result.error || '订单创建失败');
            }
        } catch (error) {
            console.error('创建订单失败:', error);
            showToast('error', '创建订单失败，请重试');
        } finally {
            // 恢复按钮状态
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        }
    }

    async function showOrderSummary(inquiryId) {
        const orderSummaryModal = new bootstrap.Modal(document.getElementById('orderSummaryModal'));
        const tbody = document.getElementById('orderRecordsBody');
        const noOrdersMessage = document.getElementById('noOrdersMessage');

        try {
            const response = await fetch(`/order/inquiry_orders/${inquiryId}`);
            const orders = await response.json();

            tbody.innerHTML = '';

            if (orders.length === 0) {
                noOrdersMessage.classList.remove('d-none');
                return;
            }

            noOrdersMessage.classList.add('d-none');
            orders.forEach(order => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                <td>${moment(order.order_date).format('YYYY-MM-DD HH:mm:ss')}</td>
                <td>${order.supplier_name || '-'}</td>
                <td>${order.quantity || 0}</td>
                <td>¥${order.price ? order.price.toFixed(2) : '0.00'}</td>
                <td>${order.delivery_days || 0}天</td>
                <td>${order.status || '-'}</td>
                <td>${order.notes || '-'}</td>
            `;
                tbody.appendChild(tr);
            });

            orderSummaryModal.show();
        } catch (error) {
            console.error('加载订单记录失败:', error);
            showToast('error', '加载订单记录失败');
        }
    }

    function getStatusBadgeClass(status) {
        switch (status) {
            case 'pending':
                return 'warning';
            case 'confirmed':
                return 'primary';
            case 'shipped':
                return 'info';
            case 'completed':
                return 'success';
            case 'cancelled':
                return 'danger';
            default:
                return 'secondary';
        }
    }

    function getStatusText(status) {
        switch (status) {
            case 'pending':
                return '待处理';
            case 'confirmed':
                return '已确认';
            case 'shipped':
                return '已发货';
            case 'completed':
                return '已完成';
            case 'cancelled':
                return '已取消';
            default:
                return '未知状态';
        }
    }

    // 添加修改订单相关函数
    let currentEditOrderId = null;
    let currentQuoteData = null;  // 添加新变量存储当前报价数据
    const editOrderModal = new bootstrap.Modal(document.getElementById('editOrderModal'));

    function showEditOrderDialog(orderId, currentQuantity, inquiryId, quoteId, supplierName, price) {
        currentEditOrderId = orderId;
        currentQuoteData = {
            inquiryId: inquiryId,
            quoteId: quoteId,
            supplierName: supplierName,
            price: price
        };
        document.getElementById('editOrderQuantity').value = currentQuantity;
        document.getElementById('editOrderNotes').value = '';
        editOrderModal.show();
    }

    async function deleteOrder() {
        if (!currentEditOrderId) {
            showToast('error', '订单ID无效');
            return;
        }

        if (!confirm('确定要删除这个订单吗？')) {
            return;
        }

        try {
            const response = await fetch(`/order/delete/${currentEditOrderId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                showToast('success', '订单删除成功');
                editOrderModal.hide();
                loadInquiries(); // 刷新列表
            } else {
                const result = await response.json();
                showToast('error', result.error || '删除失败');
            }
        } catch (error) {
            console.error('删除订单失败:', error);
            showToast('error', '删除失败，请重试');
        }
    }

    async function updateOrder() {
        // 获取提交按钮并防止重复提交
        const submitButton = document.querySelector('#editOrderModal button.btn-primary');
        if (submitButton.disabled) {
            console.log('Update order button already disabled, preventing duplicate submission');
            return;
        }

        const originalText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>更新中...';

        const quantity = parseInt(document.getElementById('editOrderQuantity').value);
        const notes = document.getElementById('editOrderNotes').value.trim();

        if (isNaN(quantity) || quantity < 1) {
            showToast('error', '请输入有效的数量（≥1）');
            // 恢复按钮状态
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
            return;
        }

        try {
            const response = await fetch(`/order/update/${currentEditOrderId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    quantity: quantity,
                    notes: notes
                })
            });

            const result = await response.json();
            if (response.ok) {
                showToast('success', '订单修改成功');
                editOrderModal.hide();
                loadInquiries(); // 刷新列表
            } else {
                showToast('error', result.error || '修改失败');
            }
        } catch (error) {
            console.error('修改订单失败:', error);
            showToast('error', '修改失败，请重试');
        } finally {
            // 恢复按钮状态
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        }
    }
</script>
{% endblock %}