import requests
from bs4 import BeautifulSoup
import json
from decimal import Decimal, ROUND_DOWN
from datetime import datetime



def get_boc_usd_rate():
    url = "https://www.boc.cn/sourcedb/whpj/enindex_1619.html"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        response.encoding = 'utf-8'

        soup = BeautifulSoup(response.text, 'html.parser')

        # 定位外层表格
        outer_table = soup.find('table', {'width': '600', 'bgcolor': '#F2F2F2'})
        if not outer_table:
            return {"error": "Outer table not found"}

        # 定位内层数据表格
        inner_table = outer_table.find('table', {'bgcolor': '#EAEAEA'})
        if not inner_table:
            return {"error": "Inner table not found"}

        # 遍历数据行
        for row in inner_table.find_all('tr')[1:]:  # 跳过表头行
            cells = row.find_all('td')
            if len(cells) >= 7 and cells[0].text.strip() == 'USD':
                cash_buying = cells[2].text.strip()
                pub_time = cells[6].text.strip().replace('\xa0', ' ')

                return {
                    "currency": "USD",
                    "cash_buying_rate": float(cash_buying),
                    "pub_time": pub_time,
                    "timestamp": datetime.now().isoformat(),
                    "source": "BOC"
                }

        return {"error": "USD rate not found"}

    except Exception as e:
        return {"error": str(e)}


def get_cmb_usd_rate():
    url = "https://fx.cmbchina.com/api/v1/fx/rate"
    headers = {
        "accept": "*/*",
        "accept-language": "en,zh-CN;q=0.9,zh;q=0.8",
        "priority": "u=1, i",
        "sec-ch-ua": "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"macOS\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "x-b3-businessid": "LB502215022803",
        "x-b3-parentspanid": "6dd538e1ed9e6ccb",
        "x-b3-sampled": "1",
        "x-b3-spanid": "fbcd457f1d38c521",
        "x-b3-timestamp": str(int(datetime.now().timestamp() * 1000)),  # 动态生成时间戳
        "x-b3-traceid": "54e230cd6dd538e1ed9e6ccbf25d4dbf",
        "Referer": "https://fx.cmbchina.com/hq/",
    }

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        data = response.json()

        if data.get("returnCode") != "SUC0000":
            return {"error": f"API返回错误: {data.get('errorMsg', '未知错误')}"}

        # 查找美元数据
        usd_entry = next(
            (item for item in data.get("body", [])
             if item.get("ccyNbr") == "美元" and item.get("ccyExc") == "10"),
            None
        )

        if not usd_entry:
            return {"error": "未找到美元汇率数据"}

        # 转换时间格式
        pub_date = usd_entry["ratDat"].replace("年", "-").replace("月", "-").replace("日", "")
        pub_time = usd_entry["ratTim"]

        return {
            "currency": "USD",
            "cash_buying_rate": float(usd_entry["rthBid"] / 100),
            "pub_date": pub_date,
            "pub_time": pub_time,
            "timestamp": datetime.now().isoformat(),
            "source": "CMB"
        }

    except requests.exceptions.RequestException as e:
        return {"error": f"请求失败: {str(e)}"}
    except json.JSONDecodeError:
        return {"error": "响应解析失败"}
    except KeyError as e:
        return {"error": f"数据字段缺失: {str(e)}"}
    


def get_forex_rate():
    """获取有效汇率并返回汇率值和提示信息"""
    rates = []
    sources = []
    
    try:
        # 获取中国银行汇率
        boc_rate = get_boc_usd_rate()
        if boc_rate.get('currency') == 'USD' and isinstance(boc_rate.get('cash_buying_rate'), (int, float)):
            rates.append(boc_rate['cash_buying_rate'])
            sources.append(f"中国银行 ({boc_rate['pub_time']})")
    except Exception as e:
        print(f"中国银行汇率获取失败: {str(e)}")

    try:
        # 获取招商银行汇率
        cmb_rate = get_cmb_usd_rate()
        if cmb_rate.get('currency') == 'USD' and isinstance(cmb_rate.get('cash_buying_rate'), (int, float)):
            rates.append(cmb_rate['cash_buying_rate'])
            sources.append(f"招商银行 ({cmb_rate['pub_time']})")
    except Exception as e:
        print(f"招商银行汇率获取失败: {str(e)}")

    if rates:
        min_rate = min(rates) 
        source_str = sources[rates.index(min_rate)]
        formatted_value = Decimal(f"{min_rate:.3f}") / Decimal(100)
        formatted_value = formatted_value.quantize(Decimal('0.00'), rounding=ROUND_DOWN)
        return float(formatted_value), f"实时汇率 {formatted_value}（数据来源：{source_str}）"
    
    return 0, "汇率获取失败：无法从中国银行或招商银行获取实时汇率"
