import os
import shutil
import logging
from datetime import datetime, timedelta
from flask import current_app
from apscheduler.schedulers.background import BackgroundScheduler
import sqlite3

logger = logging.getLogger('database_backup')

def backup_sqlite():
    """执行数据库备份和清理旧备份的函数"""
    # 从Flask配置中获取数据库路径
    with current_app.app_context():
        DB_PATH = current_app.config['DATABASE']
        BACKUP_DIR = current_app.config.get('BACKUP_DIR', 'database_backups')
        RETENTION_DAYS = current_app.config.get('BACKUP_RETENTION_DAYS', 30)
        
    # 创建备份目录
    os.makedirs(BACKUP_DIR, exist_ok=True)

    # 生成带时间戳的备份文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_name = f"backup_{timestamp}.db"
    backup_path = os.path.join(BACKUP_DIR, backup_name)

    try:
        # 使用数据库锁定确保备份时的数据一致性
        conn = sqlite3.connect(DB_PATH)
        try:
            # 开始立即事务锁定数据库
            conn.execute('BEGIN IMMEDIATE')
            
            # 执行备份
            shutil.copyfile(DB_PATH, backup_path)
            logger.info(f"数据库备份成功：{backup_path}")
            
            # 清理旧备份
            cleanup_old_backups(BACKUP_DIR, RETENTION_DAYS)
            
        finally:
            conn.close()
            
    except Exception as e:
        logger.error(f"数据库备份失败：{str(e)}")
        # 如果备份失败，可以发送通知给管理员
        notify_admin_backup_failed(str(e))

def cleanup_old_backups(backup_dir, retention_days):
    """清理超过保留天数的旧备份"""
    now = datetime.now()
    for filename in os.listdir(backup_dir):
        filepath = os.path.join(backup_dir, filename)
        if os.path.isfile(filepath):
            try:
                file_date_str = filename.split('_')[1].split('.')[0]
                file_date = datetime.strptime(file_date_str, '%Y%m%d%H%M%S')
                if (now - file_date) > timedelta(days=retention_days):
                    os.remove(filepath)
                    logger.info(f"已删除过期备份：{filename}")
            except Exception as e:
                logger.warning(f"处理备份文件时出错：{filename}, 错误：{str(e)}")
                continue

def notify_admin_backup_failed(error_message):
    """当备份失败时通知管理员"""
    logger.error(f"备份失败通知：{error_message}")

def init_backup_scheduler(app):
    scheduler = BackgroundScheduler()
    scheduler.add_jobstore('sqlalchemy', url=app.config['SQLALCHEMY_DATABASE_URI'])
    
    scheduler.add_job(
        backup_sqlite,
        'cron',
        hour=0,
        minute=0,
        id='daily_backup',
        replace_existing=True,
    )
    
    try:
        scheduler.start()
    except Exception as e:
        app.logger.error(f"Scheduler start failed: {str(e)}")
        scheduler.shutdown()