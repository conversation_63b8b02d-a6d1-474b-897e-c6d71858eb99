from app import app, db
from models import Customer, Order, ProductInquiry, Supplier, SupplierQuote, Inventory
from datetime import datetime, timedelta

def init_db():
    with app.app_context():
        # 创建所有表
        db.drop_all()
        db.create_all()
        
        # 创建测试客户
        customer1 = Customer(
            name='测试客户A',
            address='北京市朝阳区',
            phone='13800138000',
            country='中国'
        )
        customer2 = Customer(
            name='测试客户B',
            address='上海市浦东新区',
            phone='13900139000',
            country='中国'
        )
        db.session.add(customer1)
        db.session.add(customer2)
        db.session.commit()

        # 创建测试供应商
        supplier1 = Supplier(
            name='供应商A',
            contact='张三',
            default_delivery_days=30
        )
        supplier2 = Supplier(
            name='供应商B',
            contact='李四',
            default_delivery_days=45
        )
        db.session.add(supplier1)
        db.session.add(supplier2)
        db.session.commit()

        # 创建测试产品咨询
        inquiry1 = ProductInquiry(
            customer=customer1,
            brand='品牌A',
            product_name='产品A',
            quantity=100,
            expected_delivery_days=30,
            my_price=1000.00,
            inquiry_date=datetime.utcnow() - timedelta(days=5)
        )
        inquiry2 = ProductInquiry(
            customer=customer1,
            brand='品牌B',
            product_name='产品B',
            quantity=200,
            expected_delivery_days=45,
            my_price=2000.00,
            inquiry_date=datetime.utcnow() - timedelta(days=3)
        )
        db.session.add(inquiry1)
        db.session.add(inquiry2)
        db.session.commit()

        # 创建测试供应商报价
        quote1 = SupplierQuote(
            inquiry=inquiry1,
            supplier=supplier1,
            price=800.00,
            delivery_days=30
        )
        quote2 = SupplierQuote(
            inquiry=inquiry1,
            supplier=supplier2,
            price=850.00,
            delivery_days=35
        )
        quote3 = SupplierQuote(
            inquiry=inquiry2,
            supplier=supplier1,
            price=1800.00,
            delivery_days=40
        )
        db.session.add(quote1)
        db.session.add(quote2)
        db.session.add(quote3)
        db.session.commit()

        # 创建测试订单
        order1 = Order(
            customer=customer1,
            inquiry=inquiry1,
            supplier_quote=quote1,
            status='confirmed',
            notes='测试订单1',
            order_date=datetime.utcnow() - timedelta(days=2)
        )
        order2 = Order(
            customer=customer1,
            inquiry=inquiry2,
            supplier_quote=quote3,
            status='confirmed',
            notes='测试订单2',
            order_date=datetime.utcnow() - timedelta(days=1)
        )
        db.session.add(order1)
        db.session.add(order2)
        db.session.commit()

        # 创建测试库存记录
        inventory1 = Inventory(
            order_id=order1.id,
            customer_id=customer1.id,
            product_name=order1.product_name,
            quantity=order1.quantity,
            supplier_name=order1.supplier_name,
            shipping_method='sea',
            shipping_date=datetime.utcnow() + timedelta(days=15),
            serial_number='SN001'
        )
        inventory2 = Inventory(
            order_id=order2.id,
            customer_id=customer1.id,
            product_name=order2.product_name,
            quantity=order2.quantity,
            supplier_name=order2.supplier_name,
            shipping_method='air',
            shipping_date=datetime.utcnow() + timedelta(days=30),
            serial_number='SN002'
        )
        db.session.add(inventory1)
        db.session.add(inventory2)
        db.session.commit()
        
        print('数据库初始化完成，创建了以下测试数据：')
        print('- 2个客户')
        print('- 2个供应商')
        print('- 2个产品咨询')
        print('- 3个供应商报价')
        print('- 2个确认订单')
        print('- 2个库存记录')

if __name__ == '__main__':
    init_db() 