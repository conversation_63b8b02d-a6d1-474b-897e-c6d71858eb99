# Development override for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
      target: production  # Still use production target but with dev settings
    environment:
      # Development Flask Configuration
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - LOG_LEVEL=DEBUG
    
    volumes:
      # Bind mount source code for live development
      - type: bind
        source: .
        target: /app
        consistency: cached
      # Keep named volumes for data persistence
      - inquiry_data:/app/instance
      - inquiry_backups:/app/database_backups
      - inquiry_logs:/app/logs
    
    # Override command for development with auto-reload
    command: ["python", "app.py"]
    
    # Development resource limits (more generous)
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # More frequent health checks for development
    healthcheck:
      test: ["CMD", "python", "/app/healthcheck.py"]
      interval: 15s
      timeout: 5s
      retries: 2
      start_period: 20s

  # Enable git-sync service for development
  git-sync:
    profiles: []  # Remove profile restriction to always start

  # Optional: Development database browser
  db-browser:
    image: coleifer/sqlite-web:latest
    container_name: inquiry-db-browser
    ports:
      - "8080:8080"
    volumes:
      - inquiry_data:/data
    command: ["sqlite_web", "/data/quotation.db", "--host", "0.0.0.0", "--port", "8080"]
    depends_on:
      - web
    profiles:
      - development

  # Optional: Development tools container
  dev-tools:
    image: python:3.11-alpine
    container_name: inquiry-dev-tools
    volumes:
      - .:/app
      - inquiry_data:/app/instance
    working_dir: /app
    command: ["tail", "-f", "/dev/null"]  # Keep container running
    profiles:
      - development
