# Flask Application Template

A basic Flask application template with SQLAlchemy integration.

## Project Structure

```
├── app.py                # Flask application entry point
├── models.py             # Database models (SQLAlchemy)
├── requirements.txt      # Dependencies
└── README.md            # Project documentation
```

## Setup Instructions

1. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Run the application:
```bash
python app.py
```

The application will be available at http://localhost:5123

## Features

- Flask web framework
- SQLAlchemy ORM integration
- Basic User model
- SQLite database
- JSON API endpoint 