# Product Inquiry System Environment Configuration
# Copy this file to .env and update the values for your deployment

# ======================
# Security Configuration
# ======================
# Generate a secure secret key for production
# You can generate one using: python -c "import secrets; print(secrets.token_urlsafe(32))"
SECRET_KEY=your-super-secure-secret-key-here

# ======================
# Flask Configuration
# ======================
FLASK_APP=app.py
FLASK_ENV=production
FLASK_DEBUG=0

# ======================
# Database Configuration
# ======================
# SQLite (default)
DATABASE_URL=sqlite:///instance/quotation.db

# PostgreSQL (uncomment to use PostgreSQL instead of SQLite)
# DATABASE_URL=*******************************************************/inquiry_db
# POSTGRES_PASSWORD=secure_password

# MySQL (uncomment to use MySQL instead of SQLite)
# DATABASE_URL=mysql://inquiry_user:secure_password@mysql:3306/inquiry_db
# MYSQL_PASSWORD=secure_password

# ======================
# Application Configuration
# ======================
# Enable/disable automatic database backups
BACKUP_ENABLED=true

# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Instance directory path
INSTANCE_PATH=/app/instance

# ======================
# Docker Configuration
# ======================
# User and group IDs for file permissions (development only)
UID=1000
GID=1000

# ======================
# Optional: External Services
# ======================
# Redis for session storage and caching
# REDIS_URL=redis://redis:6379/0

# Email configuration for notifications
# MAIL_SERVER=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USE_TLS=true
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password

# ======================
# Monitoring and Health Checks
# ======================
# Health check interval (seconds)
HEALTH_CHECK_INTERVAL=30

# Health check timeout (seconds)
HEALTH_CHECK_TIMEOUT=10

# ======================
# File Upload Configuration
# ======================
# Maximum file size for uploads (in bytes)
MAX_CONTENT_LENGTH=16777216  # 16MB

# Allowed file extensions
ALLOWED_EXTENSIONS=pdf,doc,docx,xls,xlsx,png,jpg,jpeg,gif
