# Operating System Files
.DS_Store
Thumbs.db

# IDE/Editor
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Build artifacts
*.class
*.exe
*.dll
*.so
*.dylib
*.out
*.app

# Dependency directories
node_modules/
jspm_packages/
bower_components/
vendor/
venv/

# Logs and databases
*.log
*.sqlite
*.sql

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Temporary files
*.tmp
*.temp

# Testing
coverage/
.nyc_output/

# Python
__pycache__/
*.py[cod]

# Java
target/
build/
*.jar
*.war
*.ear

# C#
[Bb]in/
[Oo]bj/

# Go
/bin/
/vendor/

# Rust
/target/

# Node.js
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnp.*

# 根据你的项目需要添加其他忽略规则

# 新增建议添加的内容
# 数据库迁移文件
migrations/

# Flask实例文件夹
instance/

# 测试覆盖率报告
.coverage
htmlcov/

# pytest缓存
.pytest_cache/

# 本地开发环境
docker-compose.override.yml

# 日志目录
logs/

# 前端构建产物
dist/
build/
*.min.js
*.min.css

# Jupyter笔记本缓存
.ipynb_checkpoints/

# 压缩文件
*.zip
*.tar.gz

# 备份文件
*.bak
*.backup

# 本地配置文件
config.local.json
