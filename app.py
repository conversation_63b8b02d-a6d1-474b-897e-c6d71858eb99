# app.py
import json
import os
from flask import Flask, request, jsonify, render_template, redirect, url_for
from flask_cors import CORS
from models import UserActivityLog, db, Customer, ProductInquiry, SupplierQuote, Supplier, User
from datetime import datetime
from flask_migrate import Migrate
from routes.inquiry import inquiry_bp
from routes.inquiry_display import inquiry_display_bp
from routes.order import order_bp
from routes.order_display import order_display_bp
from routes.customer import customer_bp
from routes.inventory import inventory_bp
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, current_user, login_required
from flask_wtf.csrf import CSRFProtect
from routes.auth import auth_bp
from routes.forex import forex_bp
from routes.user_management import user_management_bp
from routes.audit import audit_bp
from routes.outbound import outbound_bp
import sys
from sqlalchemy import func, or_
from utils.backup_scheduler import init_backup_scheduler
import secrets
from pathlib import Path

# 使用绝对路径
BASE_DIR = Path(__file__).parent.resolve()

app = Flask(__name__)
CORS(app)  # 启用CORS支持

# 安全密钥生成函数
def generate_secure_key():
    return os.environ.get('SECRET_KEY') or secrets.token_urlsafe(32)

# 使用环境变量配置
app.config['SECRET_KEY'] = generate_secure_key()

# 确保实例目录存在
instance_path = os.path.join(os.getcwd(), 'instance')
if not os.path.exists(instance_path):
    os.makedirs(instance_path, exist_ok=True)

# 配置数据库路径
db_path = os.path.join(instance_path, 'quotation.db')
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db.init_app(app)
migrate = Migrate(app, db)

# Register blueprints
app.register_blueprint(inquiry_bp, url_prefix='/inquiry')
app.register_blueprint(inquiry_display_bp, url_prefix='/inquiry-display')
app.register_blueprint(order_bp, url_prefix='/order')
app.register_blueprint(order_display_bp, url_prefix='/order-display')
app.register_blueprint(customer_bp, url_prefix='/customer')
app.register_blueprint(inventory_bp, url_prefix='/inventory')
app.register_blueprint(auth_bp, url_prefix='/auth')
app.register_blueprint(forex_bp, url_prefix='/forex')
app.register_blueprint(user_management_bp, url_prefix='/user-management')
app.register_blueprint(audit_bp, url_prefix='/audit')
app.register_blueprint(outbound_bp, url_prefix='/outbound')

# 初始化 Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'  # 设置登录页面的端点
login_manager.login_message = '请先登录'  # 自定义提示消息

# 初始化 CSRF 保护
csrf = CSRFProtect(app)
app.config['WTF_CSRF_CHECK_DEFAULT'] = False  # 仅在需要时启用CSRF检查

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

@app.cli.command('db_init')
def db_init():
    """初始化数据库"""
    db.drop_all()
    db.create_all()
    print("Database initialized.")

# ----------------------
# 健康检查和主页
# ----------------------

@app.route('/health')
def health_check():
    """Health check endpoint for Docker health checks"""
    try:
        # Test database connection
        db.session.execute('SELECT 1')
        return jsonify({
            'status': 'healthy',
            'database': 'connected',
            'timestamp': datetime.now().isoformat()
        }), 200
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'database': 'disconnected',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 503

@app.route('/')
@login_required
def index():
    # 定义所有系统及其权限要求
    all_systems = [
        # 用户管理系统, 操作审计系统（仅管理员可见） 
        [
            {'name': '用户管理系统', 'url': '/user-management/', 'admin_only': True, 'color_class': 'primary-system'},
            {'name': '操作审计系统', 'url': '/audit/', 'admin_only': True, 'color_class': 'audit-system'}
        ],

        # 客户管理系统（仅管理员可见）
        [{'name': '客户管理系统', 'url': '/customer/', 'admin_only': True, 'color_class': 'data-system'}],

        # 产品咨询相关系统
        [
            {'name': '产品咨询管理系统', 'url': '/inquiry/', 'admin_only': False, 'color_class': 'inquiry-system'},
            {'name': '产品咨询展示系统', 'url': '/inquiry-display/', 'admin_only': True, 'color_class': 'inquiry-system'}
        ],

        # 订单相关系统（仅管理员可见）
        [
            {'name': '产品下单管理系统', 'url': '/order/', 'admin_only': True, 'color_class': 'order-system'},
            {'name': '产品下单展示系统', 'url': '/order-display/', 'admin_only': True, 'color_class': 'order-system'}
        ],

        # 出入库相关系统
        [
            {'name': '出入库管理系统', 'url': '/inventory/', 'admin_only': False, 'color_class': 'data-system'},
            {'name': '出库清单管理', 'url': '/outbound/', 'admin_only': False, 'color_class': 'data-system'}
        ]
    ]
    
    # 根据用户权限过滤系统
    filtered_systems = []
    for row in all_systems:
        filtered_row = [
            system for system in row 
            if not system['admin_only'] or current_user.is_admin
        ]
        if filtered_row:  # 只添加非空行
            filtered_systems.append(filtered_row)
    
    return render_template('index.html', systems=filtered_systems)

# ----------------------
# 客户管理
# ----------------------

@app.route('/customers', methods=['GET'])
@login_required
def get_customers():
    try:
        customers = Customer.query.order_by(Customer.name).all()
        return jsonify([{
            'id': customer.id,
            'name': customer.name
        } for customer in customers])
    except Exception as e:
        print(f"Error getting customers: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/customers', methods=['POST'])
@login_required
def create_customer():
    data = request.get_json()
    name = data.get('name', '').strip()
    
    if not name:
        return jsonify({'error': 'Customer name is required'}), 400
    
    try:
        # 检查是否已存在相同名称的客户（不区分大小写）
        existing_customer = Customer.query.filter(
            func.lower(Customer.name) == func.lower(name)
        ).first()
        
        if existing_customer:
            return jsonify({'error': 'Customer already exists'}), 400
        
        customer = Customer(name=name)
        db.session.add(customer)
        db.session.commit()
        
        return jsonify({
            'id': customer.id,
            'name': customer.name
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"Error creating customer: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ----------------------
# 产品搜索
# ----------------------

@app.route('/products/search')
@login_required
def search_products():
    query = request.args.get('query', '').strip().lower()
    customer_id = request.args.get('customer_id')
    
    print(f"Search query '{query}' for customer_id {customer_id}")
    
    if not query:
        return jsonify([])
    
    try:
        # 构建基础查询
        base_query = db.session.query(ProductInquiry).join(Customer)
        
        # 添加搜索条件（不区分大小写）
        search_conditions = [
            func.lower(ProductInquiry.product_name).like(f'%{query}%'),
            func.lower(ProductInquiry.brand).like(f'%{query}%')
        ]
        
        # 如果指定了客户ID，添加客户过滤条件
        if customer_id:
            base_query = base_query.filter(ProductInquiry.customer_id == customer_id)
        
        # 执行查询
        results = base_query.filter(or_(*search_conditions))\
            .order_by(ProductInquiry.inquiry_date.desc())\
            .limit(10)\
            .all()
        
        print(f"Found {len(results)} results")
        
        # 转换结果为JSON
        products = []
        for inquiry in results:
            product = {
                'product_name': inquiry.product_name,
                'brand': inquiry.brand,
                'quantity': inquiry.quantity,
                'my_price': inquiry.my_price,
                'expected_delivery_days': inquiry.expected_delivery_days,
                'inquiry_date': inquiry.inquiry_date.isoformat(),
                'customer_id': inquiry.customer_id,
                'customer_name': inquiry.customer.name,
                'supplier_quotes': []
            }
            
            # 添加供应商报价
            for quote in inquiry.supplier_quotes:
                product['supplier_quotes'].append({
                    'supplier_name': quote.supplier.name,
                    'supplier_contact': quote.supplier.contact,
                    'price': quote.price,
                    'delivery_days': quote.delivery_days
                })
            
            products.append(product)
        
        return jsonify(products)
        
    except Exception as e:
        print(f"Search error: {str(e)}")
        return jsonify([])

# ----------------------
# 产品咨询管理
# ----------------------

@app.route('/inquiries', methods=['POST'])
@login_required
def create_inquiry():
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400
        
    required_fields = ['product_name', 'customer_id', 'my_price', 'inquiry_date', 'quantity', 'expected_delivery_days']
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return jsonify({"error": f"Missing required fields: {', '.join(missing_fields)}"}), 400
    
    # 验证客户存在
    customer = Customer.query.get(data['customer_id'])
    if not customer:
        return jsonify({"error": f"Customer with id {data['customer_id']} not found"}), 400
    
    try:
        # 解析日期字符串
        inquiry_date = datetime.strptime(data['inquiry_date'], '%Y-%m-%d').date()
        
        # 查找是否存在相同客户的相同产品记录
        existing_inquiry = ProductInquiry.query.filter_by(
            customer_id=data['customer_id'],
            product_name=data['product_name']
        ).first()
        
        if existing_inquiry:
            # 更新现有记录
            existing_inquiry.brand = data.get('brand', '')
            existing_inquiry.quantity = int(data['quantity'])
            existing_inquiry.expected_delivery_days = int(data['expected_delivery_days'])
            existing_inquiry.my_price = float(data['my_price'])
            existing_inquiry.inquiry_date = inquiry_date
            
            # 删除现有的供应商报价
            SupplierQuote.query.filter_by(inquiry_id=existing_inquiry.id).delete()
            
            # 添加新的供应商报价
            for quote in data.get('supplier_quotes', []):
                # 查找或创建供应商
                supplier = Supplier.query.filter_by(name=quote['supplier_name']).first()
                if not supplier:
                    supplier = Supplier(
                        name=quote['supplier_name'],
                        contact=quote.get('contact_info', ''),
                        default_delivery_days=int(quote.get('delivery_days', 0))
                    )
                    db.session.add(supplier)
                    db.session.flush()  # 获取supplier.id
                
                supplier_quote = SupplierQuote(
                    inquiry=existing_inquiry,
                    supplier_id=supplier.id,
                    price=float(quote['price']),
                    delivery_days=int(quote.get('delivery_days', 0)),
                    product_name=data['product_name'],
                    brand=data.get('brand', ''),
                    quantity=int(data['quantity']),
                    my_price=float(data['my_price']),
                    inquiry_date=inquiry_date
                )
                db.session.add(supplier_quote)
                
            inquiry_id = existing_inquiry.id
        else:
            # 创建新的询价记录
            new_inquiry = ProductInquiry(
                customer_id=data['customer_id'],
                brand=data.get('brand', ''),
                product_name=data['product_name'],
                quantity=int(data['quantity']),
                expected_delivery_days=int(data['expected_delivery_days']),
                my_price=float(data['my_price']),
                inquiry_date=inquiry_date
            )
            db.session.add(new_inquiry)
            db.session.flush()  # 获取新记录的ID
            
            # 添加供应商报价
            for quote in data.get('supplier_quotes', []):
                # 查找或创建供应商
                supplier = Supplier.query.filter_by(name=quote['supplier_name']).first()
                if not supplier:
                    supplier = Supplier(
                        name=quote['supplier_name'],
                        contact=quote.get('contact_info', ''),
                        default_delivery_days=int(quote.get('delivery_days', 0))
                    )
                    db.session.add(supplier)
                    db.session.flush()  # 获取supplier.id
                
                supplier_quote = SupplierQuote(
                    inquiry=new_inquiry,
                    supplier_id=supplier.id,
                    price=float(quote['price']),
                    delivery_days=int(quote.get('delivery_days', 0)),
                    product_name=data['product_name'],
                    brand=data.get('brand', ''),
                    quantity=int(data['quantity']),
                    my_price=float(data['my_price']),
                    inquiry_date=inquiry_date
                )
                db.session.add(supplier_quote)
                
            inquiry_id = new_inquiry.id
            
        db.session.commit()
        return jsonify({
            "message": "Inquiry saved",
            "id": inquiry_id
        }), 201
        
    except Exception as e:
        db.session.rollback()
        print(f"Error saving inquiry: {str(e)}")
        return jsonify({"error": f"Failed to save inquiry: {str(e)}"}), 500

# ----------------------
# 供应商管理
# ----------------------

@app.route('/suppliers', methods=['GET'])
@login_required
def get_suppliers():
    try:
        suppliers = Supplier.query.order_by(Supplier.name).all()
        return jsonify([{
            'id': s.id,
            'name': s.name,
            'contact': s.contact,
            'default_delivery_days': s.default_delivery_days
        } for s in suppliers])
    except Exception as e:
        print(f"Error getting suppliers: {str(e)}")
        return jsonify({"error": "Failed to get suppliers"}), 500

@app.route('/suppliers', methods=['POST'])
@login_required
def save_supplier():
    data = request.json
    if not data or 'name' not in data:
        return jsonify({"error": "Supplier name is required"}), 400
    
    if not data.get('product_name'):
        return jsonify({"error": "Product name is required"}), 400
        
    # 验证必填字段
    required_fields = ['product_name', 'quantity', 'price', 'delivery_days', 'my_price']
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return jsonify({"error": f"Missing required fields: {', '.join(missing_fields)}"}), 400
        
    try:
        # 检查是否已存在相同名称的供应商
        existing_supplier = Supplier.query.filter_by(name=data['name']).first()
        if existing_supplier:
            # 更新现有供应商信息
            existing_supplier.contact = data.get('contact', '')
            existing_supplier.default_delivery_days = data.get('default_delivery_days', 0)
            supplier = existing_supplier
        else:
            # 创建新供应商
            supplier = Supplier(
                name=data['name'],
                contact=data.get('contact', ''),
                default_delivery_days=data.get('default_delivery_days', 0)
            )
            db.session.add(supplier)
        
        db.session.flush()  # 获取supplier.id
        
        try:
            inquiry_date = datetime.strptime(data.get('inquiry_date', datetime.now().strftime('%Y-%m-%d')), '%Y-%m-%d').date()
        except ValueError:
            inquiry_date = datetime.now().date()
            
        # 查找或创建产品询价记录
        inquiry = ProductInquiry.query.filter_by(
            product_name=data['product_name']
        ).order_by(ProductInquiry.inquiry_date.desc()).first()
        
        if not inquiry:
            # 如果没有现有询价，创建一个新的
            inquiry = ProductInquiry(
                customer_id=1,  # 使用默认客户ID
                product_name=data['product_name'],
                quantity=int(data['quantity']),
                expected_delivery_days=int(data['delivery_days']),
                my_price=float(data['my_price']),
                brand=data.get('brand', ''),
                inquiry_date=inquiry_date
            )
            db.session.add(inquiry)
            db.session.flush()  # 获取inquiry.id
        else:
            # 更新现有询价
            inquiry.quantity = int(data['quantity'])
            inquiry.expected_delivery_days = int(data['delivery_days'])
            inquiry.my_price = float(data['my_price'])
            inquiry.brand = data.get('brand', '')
            inquiry.inquiry_date = inquiry_date
            
        # 查找是否已存在该供应商对该产品的报价
        existing_quote = SupplierQuote.query.filter_by(
            supplier_id=supplier.id,
            inquiry_id=inquiry.id
        ).first()
        
        if existing_quote:
            # 更新现有报价
            existing_quote.price = float(data.get('price', 0))
            existing_quote.delivery_days = int(data.get('delivery_days', 0))
            existing_quote.brand = data.get('brand', '')
            existing_quote.quantity = int(data.get('quantity', 0))
            existing_quote.my_price = float(data.get('my_price', 0))
            existing_quote.inquiry_date = inquiry_date
        else:
            # 创建新的报价记录
            supplier_quote = SupplierQuote(
                inquiry_id=inquiry.id,
                supplier_id=supplier.id,
                price=float(data.get('price', 0)),
                delivery_days=int(data.get('delivery_days', 0)),
                product_name=data['product_name'],
                brand=data.get('brand', ''),
                quantity=int(data.get('quantity', 0)),
                my_price=float(data.get('my_price', 0)),
                inquiry_date=inquiry_date
            )
            db.session.add(supplier_quote)
        
        db.session.commit()
        
        # 查找该供应商对该产品的所有历史报价
        historical_quotes = SupplierQuote.query.filter_by(
            supplier_id=supplier.id,
            product_name=data['product_name']
        ).order_by(SupplierQuote.inquiry_date.desc()).all()

        # 添加审计日志
        log = UserActivityLog(
            user_id=current_user.id,
            action_type="保存供应商",
            details=json.dumps({
                "brand": data.get('brand', ''),
                "product_name": data['product_name'],
                "supplier_name": supplier.name,
                "quantity": data['quantity'],
                "price": data['price'],
                "delivery_days": data['delivery_days'],
                "inquiry_date": inquiry_date.isoformat(),
            }),
            ip_address=request.remote_addr
        )
        db.session.add(log)
        db.session.commit()
        
        return jsonify({
            'name': supplier.name,
            'contact': supplier.contact,
            'default_delivery_days': supplier.default_delivery_days,
            'historical_quotes': [{
                'price': quote.price,
                'delivery_days': quote.delivery_days,
                'inquiry_date': quote.inquiry_date.isoformat(),
                'product_name': quote.product_name,
                'brand': quote.brand,
                'quantity': quote.quantity,
                'supplier_contact': supplier.contact
            } for quote in historical_quotes],
            'message': 'Supplier and quote saved'
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"Error saving supplier and quote: {str(e)}")
        return jsonify({"error": "Failed to save supplier and quote"}), 500

@app.route('/suppliers/search', methods=['GET'])
@login_required
def search_suppliers():
    query = request.args.get('q', '').strip().lower()
    
    if not query:
        return jsonify([])
    
    try:
        # 直接从Supplier表中查询
        suppliers = Supplier.query.filter(
            func.lower(Supplier.name).like(f'%{query}%')
        ).all()
        
        return jsonify([{
            'name': supplier.name,
            'contact': supplier.contact,
            'default_delivery_days': supplier.default_delivery_days
        } for supplier in suppliers])
        
    except Exception as e:
        print(f"Supplier search error: {str(e)}")
        return jsonify([])

@app.route('/suppliers/history', methods=['GET'])
@login_required
def get_supplier_history():
    supplier_name = request.args.get('supplier_name')
    product_name = request.args.get('product_name')
    
    if not supplier_name or not product_name:
        return jsonify({"error": "Supplier name and product name are required"}), 400
        
    try:
        # 先找到供应商
        supplier = Supplier.query.filter_by(name=supplier_name).first()
        if not supplier:
            return jsonify({"error": "Supplier not found"}), 404
            
        # 查找供应商的历史报价信息
        historical_quotes = SupplierQuote.query.filter_by(
            supplier_id=supplier.id,
            product_name=product_name
        ).order_by(SupplierQuote.inquiry_date.desc()).all()
        
        return jsonify({
            'supplier_name': supplier.name,
            'product_name': product_name,
            'historical_quotes': [{
                'price': quote.price,
                'delivery_days': quote.delivery_days,
                'inquiry_date': quote.inquiry_date.isoformat(),
                'brand': quote.brand,
                'quantity': quote.quantity
            } for quote in historical_quotes]
        })
        
    except Exception as e:
        print(f"Error getting supplier history: {str(e)}")
        return jsonify({"error": "Failed to get supplier history"}), 500

@app.route('/suppliers/product-history', methods=['GET'])
@login_required
def get_product_supplier_history():
    product_name = request.args.get('product_name', '').strip()
    supplier_name = request.args.get('supplier_name', '').strip()
    
    try:
        query = SupplierQuote.query
        
        # 如果提供了产品名称，按产品名称筛选
        if product_name:
            query = query.filter(func.lower(SupplierQuote.product_name) == func.lower(product_name))
            
        # 如果提供了供应商名称，按供应商名称筛选
        if supplier_name:
            query = query.join(Supplier).filter(func.lower(Supplier.name) == func.lower(supplier_name))
            
        # 如果两个参数都没提供，返回错误
        if not product_name and not supplier_name:
            return jsonify({'error': 'Product name or supplier name is required'}), 400
            
        quotes = query.order_by(
            SupplierQuote.supplier_id,
            SupplierQuote.inquiry_date.desc()
        ).all()
        
        # 按供应商名称分组，只保留每个供应商的最新报价
        latest_quotes = {}
        for quote in quotes:
            supplier_key = quote.supplier.name.lower()
            if supplier_key not in latest_quotes:
                latest_quotes[supplier_key] = {
                    'supplier_name': quote.supplier.name,
                    'supplier_contact': quote.supplier.contact,
                    'product_name': quote.product_name,
                    'brand': quote.brand,
                    'price': quote.price,
                    'delivery_days': quote.delivery_days,
                    'inquiry_date': quote.inquiry_date.isoformat()
                }
        
        print(f"Found {len(latest_quotes)} supplier quotes")
        
        return jsonify({
            'product_name': product_name,
            'supplier_name': supplier_name,
            'supplier_quotes': list(latest_quotes.values())
        })
        
    except Exception as e:
        print(f"Error getting supplier history: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ----------------------
# 登录页面
# ----------------------

@app.route('/auth/login')
def login_page():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    return render_template('auth/login.html')

@app.route('/auth/register')
def register_page():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    return render_template('auth/register.html')

@app.cli.command('create-users')
def create_users():
    """创建管理员和普通用户账号"""
    users = [
        {'username': 'admin', 'password': 'szbk0755', 'is_admin': True},
        {'username': 'user', 'password': 'test123', 'is_admin': False},
        {'username': 'root', 'password': 'test123', 'is_admin': True}
    ]
    
    for user_data in users:
        username = user_data['username']
        if User.query.filter_by(username=username).first():
            print(f'User {username} already exists')
            continue
            
        user = User(
            username=username,
            is_admin=user_data['is_admin']
        )
        user.set_password(user_data['password'])
        db.session.add(user)
        db.session.commit()
        print(f'Created {"admin" if user_data["is_admin"] else "normal"} user: {username}')

# ----------------------
# 添加日志记录中间件
# ----------------------

@app.after_request
def log_user_activity(response):
    if current_user.is_authenticated and request.method in ['POST', 'PUT', 'DELETE']:
        try:
            # 记录关键操作
            log = UserActivityLog(
                user_id=current_user.id,
                action_type=f"{request.method} {request.path}",
                details=f"Status: {response.status_code}",
                ip_address=request.remote_addr
            )
            db.session.add(log)
            db.session.commit()
        except Exception as e:
            app.logger.error(f"Failed to log activity: {str(e)}")
    return response

@app.context_processor
def utility_processor():
    def get_host_info():
        import socket
        hostname = socket.gethostname()
        try:
            ip = socket.gethostbyname(hostname)
        except:
            ip = '未知'
        return {'hostname': hostname, 'ip': ip}
    return dict(get_host_info=get_host_info)

# ----------------------
# 启动应用
# ----------------------

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'db_init':
        with app.app_context():
            instance_path = app.instance_path
            if not os.path.exists(instance_path):
                os.makedirs(instance_path)
            db.drop_all()
            db.create_all()
    else:
        # 配置数据库备份设置
        app.config['DATABASE'] = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
        app.config.update(
            BACKUP_DIR=os.path.join(BASE_DIR, 'database_backups'),
            BACKUP_RETENTION_DAYS=90
        )

        app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
            'connect_args': {
                'timeout': 30,
                'check_same_thread': False
            }
        }
        
        # 初始化备份调度器
        with app.app_context():
            init_backup_scheduler(app)
            
        # 打印访问信息
        import socket
        hostname = socket.gethostname()
        try:
            ip = socket.gethostbyname(hostname)
            print(f"\n应用已启动！")
            print(f"局域网访问地址: http://{ip}:5123")
            print(f"本地访问地址: http://localhost:5123\n")
        except:
            print("无法获取IP地址信息")
            
        port = int(os.environ.get('FLASK_RUN_PORT', 5123))
        app.run(host='0.0.0.0', port=port, debug=True)
