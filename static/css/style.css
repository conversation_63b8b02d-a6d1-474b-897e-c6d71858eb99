/* 卡片样式 */
.card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;
    transition: box-shadow 0.3s ease;
    background: #fff;
    border-radius: 8px;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-body {
    padding: 1.75rem;
    position: relative;
}

.card-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

/* 搜索框容器 */
.search-container {
    position: relative;
    margin-bottom: 1rem;
}

/* 搜索框样式 */
.input-group {
    margin-bottom: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    transition: all 0.3s ease;
    width: 100%;
}

.input-group.show-results {
    border-radius: 4px 4px 0 0;
    border-bottom-color: transparent;
}

.input-group:focus-within {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
}

/* 表单标签样式 */
.form-label {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.form-label.required:after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* 输入框样式 */
.form-control {
    border: 1px solid #ced4da;
    padding: 0.625rem 0.75rem;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

.form-control:hover {
    border-color: #b3d7ff;
}

/* 下拉选择框样式 */
.form-select {
    padding: 0.625rem 2.25rem 0.625rem 0.75rem;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

.form-select:hover {
    border-color: #b3d7ff;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.btn-outline-primary {
    border-width: 2px;
}

.btn-outline-danger {
    border-width: 2px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-sm {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

/* 供应商报价样式 */
.supplier-quote {
    padding: 1.25rem;
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.supplier-quote:hover {
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 输入组样式 */
.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
    color: #495057;
    font-weight: 500;
    transition: all 0.3s ease;
}

.input-group:focus-within .input-group-text {
    border-color: #80bdff;
    background-color: #e9ecef;
}

/* 表单文本样式 */
.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* 搜索结果样式 */
#searchResults {
    position: absolute;
    top: 100%;
    left: 0;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1050;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e9ecef;
    border-top: none;
    border-radius: 0 0 4px 4px;
    background-color: white;
    margin-top: -1px;
}

#searchResults .list-group-item {
    cursor: pointer;
    border: none;
    border-bottom: 1px solid #e9ecef;
    padding: 12px 16px;
    transition: all 0.2s ease;
}

#searchResults .list-group-item:last-child {
    border-bottom: none;
}

#searchResults .list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(4px);
}

#searchResults .list-group-item h6 {
    margin: 0;
    color: #2c3e50;
    font-weight: 500;
}

#searchResults .list-group-item small {
    color: #6c757d;
}

#searchResults .list-group-item .supplier-info {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px dashed #e9ecef;
}

/* 滚动条样式 */
#searchResults::-webkit-scrollbar {
    width: 6px;
}

#searchResults::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#searchResults::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#searchResults::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 加载状态指示器 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading:after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* 错误提示样式 */
.invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 必填字段标记样式 */
.required:after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-body {
        padding: 1.25rem;
    }
    
    .supplier-quote {
        padding: 1rem;
    }
    
    .supplier-quote .row {
        margin-bottom: 0.75rem;
    }
    
    .supplier-quote .col-md-2 {
        margin-top: 0.75rem;
    }
} 