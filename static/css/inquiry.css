/* 搜索框容器 */
.search-container {
    position: relative;
    margin-bottom: 1rem;
}

/* 搜索框样式 */
.input-group {
    margin-bottom: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    transition: all 0.3s ease;
    width: 100%;
}

.input-group.show-results {
    border-radius: 4px 4px 0 0;
    border-bottom-color: transparent;
}

.input-group:focus-within {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
}

/* 搜索结果样式 */
#searchResults {
    position: absolute;
    top: 100%;
    left: 0;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1050;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e9ecef;
    border-top: none;
    border-radius: 0 0 4px 4px;
    background-color: white;
    margin-top: -1px;
}

#searchResults .list-group-item {
    cursor: pointer;
    border: none;
    border-bottom: 1px solid #e9ecef;
    padding: 12px 16px;
    transition: all 0.2s ease;
}

#searchResults .list-group-item:last-child {
    border-bottom: none;
}

#searchResults .list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(4px);
}

#searchResults .list-group-item h6 {
    margin: 0;
    color: #2c3e50;
    font-weight: 500;
}

#searchResults .list-group-item small {
    color: #6c757d;
}

#searchResults .list-group-item .supplier-info {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px dashed #e9ecef;
}

/* 滚动条样式 */
#searchResults::-webkit-scrollbar {
    width: 6px;
}

#searchResults::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#searchResults::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#searchResults::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 加载状态指示器 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading:after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* 供应商报价区域样式 */
.supplier-quote {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.supplier-quote:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.supplier-quote .form-label {
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 0.5rem;
}

.supplier-quote .input-group {
    box-shadow: none;
    transition: all 0.2s ease;
}

.supplier-quote .input-group:focus-within {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
}

.supplier-quote .btn-group {
    gap: 8px;
}

.supplier-quote .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.supplier-quote .bi {
    font-size: 1.1em;
}

/* 交货周期区域样式 */
.supplier-quote .row.g-2 {
    margin: 0 -5px;
}

.supplier-quote .row.g-2 > div {
    padding: 0 5px;
}

.supplier-quote .total-days {
    font-weight: 600;
    color: #0d6efd;
}

/* 添加供应商报价按钮样式 */
.btn-add-supplier {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.btn-add-supplier .bi {
    font-size: 1.2em;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .supplier-quote {
        padding: 15px;
    }
    
    .supplier-quote .row + .row {
        margin-top: 1rem;
    }
    
    .supplier-quote .btn-group {
        margin-top: 1rem;
    }
}

/* 搜索容器样式 */
.search-container {
    position: relative;
}

#searchResults {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    background: white;
    border: 1px solid rgba(0,0,0,.125);
    border-radius: 0.25rem;
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
    display: none;
}

.search-container.show-results #searchResults {
    display: block;
}

/* 基础样式 */
body {
    font-size: 16px;
    line-height: 1.6;
}

/* 标题样式 */
h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

h5.card-title {
    font-size: 1.4rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

h6 {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

/* 表单样式 */
.form-label {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    font-size: 1rem;
    padding: 0.5rem 0.75rem;
}

.input-group-text {
    font-size: 1rem;
}

/* 按钮样式 */
.btn {
    font-size: 1rem;
    padding: 0.5rem 1rem;
}

.btn-sm {
    font-size: 0.9rem;
    padding: 0.4rem 0.8rem;
}

/* 搜索结果样式 */
#searchResults .list-group-item {
    font-size: 1rem;
    padding: 1rem;
}

#searchResults h6 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

#searchResults .small {
    font-size: 0.9rem;
}

/* 供应商报价样式 */
.supplier-quote {
    font-size: 1rem;
}

/* 辅助文本样式 */
.form-text {
    font-size: 0.9rem;
    color: #6c757d;
}

/* 徽章样式 */
.badge {
    font-size: 0.85rem;
    padding: 0.4em 0.6em;
}

/* 模态框样式 */
.modal-title {
    font-size: 1.4rem;
}

.modal-body {
    font-size: 1rem;
}

.supplier-quotes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.supplier-quotes-list {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 1rem;
} 