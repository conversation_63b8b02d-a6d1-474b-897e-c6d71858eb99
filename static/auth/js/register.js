// 密码强度检查
function checkPasswordStrength(password) {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
}

// 更新密码强度指示器
function updatePasswordStrength(password) {
    const strengthEl = document.querySelector('.password-strength');
    const strength = checkPasswordStrength(password);
    
    strengthEl.classList.remove('weak', 'medium', 'strong');
    if (strength > 0) {
        strengthEl.classList.add(
            strength <= 2 ? 'weak' : 
            strength <= 3 ? 'medium' : 'strong'
        );
    }
}

document.getElementById('password')?.addEventListener('input', function() {
    updatePasswordStrength(this.value);
});

document.getElementById('register-form')?.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    Auth.clearErrors(this);
    const errors = Auth.validateForm(this);
    
    // 额外的密码确认验证
    if (this.password.value !== this.confirm_password.value) {
        errors.confirm_password = '两次输入的密码不一致';
    }
    
    if (Object.keys(errors).length > 0) {
        for (const [field, message] of Object.entries(errors)) {
            Auth.showError(this[field], message);
        }
        return;
    }
    
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    
    try {
        await Auth.fetchAPI('/auth/register', {
            method: 'POST',
            body: JSON.stringify({
                username: this.username.value.trim(),
                password: this.password.value,
                confirm_password: this.confirm_password.value
            })
        });
        
        // 注册成功后自动登录
        await Auth.fetchAPI('/auth/login', {
            method: 'POST',
            body: JSON.stringify({
                username: this.username.value.trim(),
                password: this.password.value
            })
        });
        
        window.location.href = '/';
        
    } catch (error) {
        const errorAlert = document.getElementById('register-error');
        errorAlert.textContent = error.message;
        errorAlert.classList.remove('d-none');
    } finally {
        submitBtn.disabled = false;
    }
}); 