// 通用工具函数
const Auth = {
    // CSRF令牌处理
    getCSRFToken() {
        return document.querySelector('input[name="csrf_token"]')?.value;
    },

    // API调用封装
    async fetchAPI(url, options = {}) {
        const defaultOptions = {
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            }
        };

        try {
            const response = await fetch(url, { ...defaultOptions, ...options });
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || '请求失败');
            }
            
            return data;
        } catch (error) {
            throw error;
        }
    },

    // 表单验证
    validateForm(form) {
        const username = form.username.value.trim();
        const password = form.password.value;
        const errors = {};

        if (!username) {
            errors.username = '用户名不能为空';
        } else if (username.length < 3) {
            errors.username = '用户名至少需要3个字符';
        }

        if (!password) {
            errors.password = '密码不能为空';
        } else if (password.length < 6) {
            errors.password = '密码至少需要6个字符';
        }

        return errors;
    },

    // 显示错误信息
    showError(element, message) {
        element.classList.add('is-invalid');
        const feedback = element.nextElementSibling;
        if (feedback) {
            feedback.textContent = message;
        }
    },

    // 清除错误信息
    clearErrors(form) {
        form.querySelectorAll('.is-invalid').forEach(element => {
            element.classList.remove('is-invalid');
        });
        form.querySelectorAll('.invalid-feedback').forEach(element => {
            element.textContent = '';
        });
        form.querySelector('.alert')?.classList.add('d-none');
    },

    // 检查登录状态
    async checkAuthStatus() {
        try {
            const data = await this.fetchAPI('/auth/check');
            this.updateNavbar(data.logged_in, data.username);
            return data.logged_in;
        } catch (error) {
            console.error('检查登录状态失败:', error);
            return false;
        }
    },

    // 更新导航栏
    updateNavbar(isLoggedIn, username) {
        const authStatus = document.getElementById('auth-status');
        if (!authStatus) return;

        if (isLoggedIn) {
            authStatus.innerHTML = `
                <div class="dropdown">
                    <button class="btn btn-link nav-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        ${username}
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="Auth.logout()">退出登录</a></li>
                    </ul>
                </div>
            `;
        } else {
            authStatus.innerHTML = `
                <a class="nav-link" href="/auth/login">登录</a>
            `;
        }
    },

    // 登出处理
    async logout() {
        try {
            await this.fetchAPI('/auth/logout', { method: 'POST' });
            window.location.href = '/';
        } catch (error) {
            console.error('登出失败:', error);
        }
    },

    // 会话检查
    startSessionCheck() {
        setInterval(() => this.checkAuthStatus(), 5 * 60 * 1000); // 每5分钟检查一次
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    Auth.checkAuthStatus();
    Auth.startSessionCheck();
}); 