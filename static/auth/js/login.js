document.getElementById('login-form')?.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    Auth.clearErrors(this);
    const errors = Auth.validateForm(this);
    
    if (Object.keys(errors).length > 0) {
        for (const [field, message] of Object.entries(errors)) {
            Auth.showError(this[field], message);
        }
        return;
    }
    
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    
    try {
        const response = await Auth.fetchAPI('/auth/login', {
            method: 'POST',
            body: JSON.stringify({
                username: this.username.value.trim(),
                password: this.password.value
            })
        });
        
        // 登录成功，重定向到之前的页面或首页
        const returnUrl = new URLSearchParams(window.location.search).get('next') || '/';
        window.location.href = returnUrl;
        
    } catch (error) {
        const errorAlert = document.getElementById('login-error');
        errorAlert.textContent = error.message;
        errorAlert.classList.remove('d-none');
    } finally {
        submitBtn.disabled = false;
    }
}); 