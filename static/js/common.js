class GlobalToast {
    static container = null;
    static activeToasts = [];

    static init() {
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'toast-container';
            this.container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
            `;
            document.body.appendChild(this.container);
        }
    }

    static show(type = 'success', message, duration = 3000) {
        this.init();
        
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        const icon = document.createElement('span');
        icon.className = 'toast-icon';
        switch (type) {
            case 'success':
                icon.innerHTML = '✓';
                break;
            case 'error':
                icon.innerHTML = '✕';
                break;
            case 'warning':
                icon.innerHTML = '!';
                break;
        }
        
        const text = document.createElement('span');
        text.className = 'toast-text';
        text.textContent = message;
        
        toast.appendChild(icon);
        toast.appendChild(text);
        
        // Add to container
        this.container.appendChild(toast);
        
        // Trigger animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        // Auto remove
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }
}

// For backward compatibility - matches existing parameter order (type, message)
function showToast(type = 'success', message, duration = 3000) {
    GlobalToast.show(type, message, duration);
} 