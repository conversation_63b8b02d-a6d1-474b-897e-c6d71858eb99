#!/bin/bash

# Product Inquiry System - macOS Startup Script
# This script sets up and runs the Product Inquiry System on macOS

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="Product Inquiry System"
VENV_NAME="venv"
DEFAULT_PORT=5124
PYTHON_VERSION="3.9"

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check system prerequisites
check_system_prerequisites() {
    print_status "Checking system prerequisites..."

    # Check if we're on macOS
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_error "This script is designed for macOS only"
        return 1
    fi

    # Check for required system tools
    local required_tools=("curl" "git" "xcode-select")
    local missing_tools=()

    for tool in "${required_tools[@]}"; do
        if ! command_exists "$tool"; then
            missing_tools+=("$tool")
        fi
    done

    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required system tools: ${missing_tools[*]}"
        print_status "Please install Xcode Command Line Tools: xcode-select --install"
        return 1
    fi

    # Check available disk space (at least 1GB)
    local available_space=$(df -g . | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 1 ]; then
        print_warning "Low disk space detected (${available_space}GB available). Installation may fail."
    fi

    print_success "System prerequisites check passed"
    return 0
}

# Function to install Homebrew if not present
install_homebrew() {
    if ! command_exists brew; then
        print_status "Installing Homebrew..."

        # Check if we're on macOS
        if [[ "$OSTYPE" != "darwin"* ]]; then
            print_error "Homebrew is only supported on macOS"
            return 1
        fi

        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

        # Add Homebrew to PATH for Apple Silicon Macs
        if [[ $(uname -m) == "arm64" ]]; then
            # Check if already in .zshrc to avoid duplicates
            if ! grep -q '/opt/homebrew/bin/brew shellenv' ~/.zshrc 2>/dev/null; then
                echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zshrc
            fi
            eval "$(/opt/homebrew/bin/brew shellenv)"
        else
            # Intel Macs
            if ! grep -q '/usr/local/bin/brew shellenv' ~/.zshrc 2>/dev/null; then
                echo 'eval "$(/usr/local/bin/brew shellenv)"' >> ~/.zshrc
            fi
            eval "$(/usr/local/bin/brew shellenv)"
        fi
        print_success "Homebrew installed successfully"
    else
        local brew_version=$(brew --version | head -n1)
        print_status "Homebrew already installed: $brew_version"

        # Update Homebrew if it's been a while
        print_status "Updating Homebrew..."
        brew update || print_warning "Failed to update Homebrew, continuing..."
    fi
}

# Function to install Python if not present
install_python() {
    if ! command_exists python3; then
        print_status "Installing Python 3.11..."

        # Check if Homebrew is available
        if ! command_exists brew; then
            print_error "Homebrew is required to install Python. Please install Homebrew first."
            return 1
        fi

        brew install python@3.11
        print_success "Python installed successfully"
    else
        local python_version=$(python3 --version 2>&1)
        print_status "Python already installed: $python_version"

        # Check if we have a compatible version
        local version_check=$(python3 -c "import sys; print(sys.version_info >= (3, 9))" 2>/dev/null || echo "False")
        if [[ "$version_check" == "False" ]]; then
            print_warning "Python version may be too old (< 3.9). Consider upgrading."
            print_status "Attempting to install Python 3.11 via Homebrew..."
            brew install python@3.11 || print_warning "Failed to install newer Python version"
        fi
    fi

    # Ensure pip is available
    if ! command_exists pip3; then
        print_status "Installing pip..."
        python3 -m ensurepip --upgrade || print_warning "Failed to install pip"
    fi
}

# Function to install uv if not present
install_uv() {
    if ! command_exists uv; then
        print_status "Installing uv (fast Python package installer)..."

        # Check if curl is available
        if ! command_exists curl; then
            print_error "curl is required to install uv. Please install curl first."
            return 1
        fi

        curl -LsSf https://astral.sh/uv/install.sh | sh

        # Add uv to PATH if not already there
        if [[ ":$PATH:" != *":$HOME/.cargo/bin:"* ]]; then
            export PATH="$HOME/.cargo/bin:$PATH"
        fi

        # Add to shell profile if not already there
        if ! grep -q 'export PATH="$HOME/.cargo/bin:$PATH"' ~/.zshrc 2>/dev/null; then
            echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> ~/.zshrc
        fi

        # Verify installation
        if command_exists uv; then
            print_success "uv installed successfully: $(uv --version)"
        else
            print_warning "uv installation may have failed. Will fall back to pip."
        fi
    else
        local uv_version=$(uv --version 2>/dev/null || echo "unknown")
        print_status "uv already installed: $uv_version"

        # Check if uv is up to date (optional)
        print_status "Checking for uv updates..."
        uv self update 2>/dev/null || print_status "uv update check skipped"
    fi
}

# Function to install Node.js and npm if not present
install_nodejs() {
    if ! command_exists node; then
        print_status "Installing Node.js..."

        # Check if Homebrew is available
        if ! command_exists brew; then
            print_error "Homebrew is required to install Node.js. Please install Homebrew first."
            return 1
        fi

        brew install node

        # Verify installation
        if command_exists node && command_exists npm; then
            print_success "Node.js installed successfully: $(node --version)"
            print_status "npm version: $(npm --version)"
        else
            print_warning "Node.js installation may have failed"
            return 1
        fi
    else
        local node_version=$(node --version 2>/dev/null || echo "unknown")
        local npm_version=$(npm --version 2>/dev/null || echo "unknown")
        print_status "Node.js already installed: $node_version"
        print_status "npm version: $npm_version"

        # Check if Node.js version is compatible (>= 16.0.0)
        local version_check=$(node -e "console.log(process.version.slice(1).split('.')[0] >= 16)" 2>/dev/null || echo "false")
        if [[ "$version_check" == "false" ]]; then
            print_warning "Node.js version may be too old (< 16.0.0). Consider upgrading."
            print_status "Attempting to upgrade Node.js via Homebrew..."
            brew upgrade node || print_warning "Failed to upgrade Node.js"
        fi

        # Update npm to latest version
        print_status "Updating npm to latest version..."
        npm install -g npm@latest 2>/dev/null || print_status "npm update skipped"
    fi
}

# Function to setup Python virtual environment
setup_python_env() {
    print_status "Setting up Python virtual environment..."

    # Check if requirements.txt exists
    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt not found. Cannot install Python dependencies."
        return 1
    fi

    if [ ! -d "$VENV_NAME" ]; then
        print_status "Creating virtual environment..."
        python3 -m venv "$VENV_NAME"
        print_success "Virtual environment created"
    else
        print_status "Virtual environment already exists"
    fi

    # Activate virtual environment
    source "$VENV_NAME/bin/activate"
    print_status "Virtual environment activated"

    # Check if pip needs upgrading
    local pip_version=$(pip --version | cut -d' ' -f2)
    print_status "Current pip version: $pip_version"

    # Upgrade pip if needed
    print_status "Upgrading pip..."
    pip install --upgrade pip --quiet

    # Check if dependencies are already installed
    local deps_installed=true
    if command_exists uv; then
        print_status "Checking existing dependencies with uv..."
        if ! uv pip check 2>/dev/null; then
            deps_installed=false
        fi
    else
        # Basic check for Flask (main dependency)
        if ! python -c "import flask" 2>/dev/null; then
            deps_installed=false
        fi
    fi

    # Install dependencies if needed
    if [ "$deps_installed" = false ]; then
        if command_exists uv; then
            print_status "Installing Python dependencies with uv..."
            uv pip install -r requirements.txt
        else
            print_status "Installing Python dependencies with pip..."
            pip install -r requirements.txt
        fi
        print_success "Python dependencies installed"
    else
        print_status "Python dependencies already satisfied"

        # Optional: Check for updates
        if command_exists uv; then
            print_status "Checking for dependency updates..."
            uv pip install -r requirements.txt --upgrade 2>/dev/null || print_status "Dependency update check skipped"
        fi
    fi
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."
    
    # Activate virtual environment
    source "$VENV_NAME/bin/activate"
    
    # Create instance directory if it doesn't exist
    mkdir -p instance
    
    # Initialize database if it doesn't exist
    if [ ! -f "instance/quotation.db" ]; then
        print_status "Initializing database..."
        python -c "
from app import app, db
with app.app_context():
    db.create_all()
    print('Database initialized successfully')
"
        
        # Create default users
        print_status "Creating default users..."
        python -c "
from app import app, db
from models import User
with app.app_context():
    # Create admin user
    if not User.query.filter_by(username='admin').first():
        admin = User(username='admin', is_admin=True)
        admin.set_password('szbk0755')
        db.session.add(admin)
        print('Created admin user: admin/szbk0755')
    
    # Create regular user
    if not User.query.filter_by(username='user').first():
        user = User(username='user', is_admin=False)
        user.set_password('test123')
        db.session.add(user)
        print('Created regular user: user/test123')
    
    db.session.commit()
    print('Default users created successfully')
"
        print_success "Database setup completed"
    else
        print_status "Database already exists"
    fi
}

# Function to install front-end dependencies
setup_frontend() {
    print_status "Setting up front-end dependencies..."

    # Check if package.json exists
    if [ -f "package.json" ]; then
        # Check if node_modules exists and is up to date
        if [ -d "node_modules" ] && [ -f "package-lock.json" ]; then
            # Check if package-lock.json is newer than package.json
            if [ "package-lock.json" -nt "package.json" ]; then
                print_status "npm dependencies appear to be up to date"

                # Quick verification that key dependencies exist
                if [ -d "node_modules/bootstrap" ] || [ -d "node_modules/.bin" ]; then
                    print_status "Skipping npm install - dependencies already installed"
                else
                    print_status "Installing missing npm dependencies..."
                    npm install
                    print_success "npm dependencies installed"
                fi
            else
                print_status "package.json is newer than package-lock.json, updating dependencies..."
                npm install
                print_success "npm dependencies updated"
            fi
        else
            print_status "Installing npm dependencies..."
            npm install
            print_success "npm dependencies installed"
        fi

        # Check for any front-end build process
        if npm run 2>/dev/null | grep -q "build"; then
            print_status "Building front-end assets..."
            npm run build
            print_success "Front-end build completed"
        fi
    else
        print_status "No package.json found, skipping npm dependencies"
    fi
}

# Function to check port availability
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null; then
        return 1  # Port is in use
    else
        return 0  # Port is available
    fi
}

# Function to find available port
find_available_port() {
    local port=$DEFAULT_PORT
    while ! check_port $port; do
        print_warning "Port $port is in use, trying $((port + 1))"
        port=$((port + 1))
    done
    echo $port
}

# Function to start the service
start_service() {
    print_status "Starting $PROJECT_NAME..."
    
    # Activate virtual environment
    source "$VENV_NAME/bin/activate"
    
    # Find available port
    PORT=$(find_available_port)
    
    print_status "Starting service on port $PORT..."
    
    # Set environment variables
    export FLASK_ENV=development
    export FLASK_DEBUG=1
    export FLASK_RUN_PORT=$PORT
    
    # Start the application
    print_success "$PROJECT_NAME is starting..."
    print_success "Access the application at: http://localhost:$PORT"
    print_success "Default admin login: admin/szbk0755"
    print_success "Default user login: user/test123"
    print_success "Press Ctrl+C to stop the service"
    
    python app.py
}

# Function to show installation summary
show_installation_summary() {
    print_status "Installation Summary:"
    echo "  ✓ Operating System: $(sw_vers -productName) $(sw_vers -productVersion)"

    if command_exists brew; then
        echo "  ✓ Homebrew: $(brew --version | head -n1)"
    else
        echo "  ✗ Homebrew: Not installed"
    fi

    if command_exists python3; then
        echo "  ✓ Python: $(python3 --version)"
    else
        echo "  ✗ Python: Not installed"
    fi

    if command_exists uv; then
        echo "  ✓ uv: $(uv --version)"
    else
        echo "  ✗ uv: Not installed (will use pip)"
    fi

    if command_exists node; then
        echo "  ✓ Node.js: $(node --version)"
        echo "  ✓ npm: $(npm --version)"
    else
        echo "  ✗ Node.js: Not installed"
    fi

    if [ -d "$VENV_NAME" ]; then
        echo "  ✓ Virtual Environment: Created"
    else
        echo "  ✗ Virtual Environment: Not created"
    fi

    if [ -f "instance/quotation.db" ]; then
        echo "  ✓ Database: Initialized"
    else
        echo "  ✗ Database: Not initialized"
    fi

    echo ""
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --help, -h          Show this help message"
    echo "  --install-only      Only install dependencies, don't start service"
    echo "  --skip-deps         Skip dependency installation, only start service"
    echo "  --port PORT         Specify port number (default: $DEFAULT_PORT)"
    echo "  --reset-db          Reset database (WARNING: This will delete all data)"
    echo ""
    echo "Examples:"
    echo "  $0                  Install dependencies and start service"
    echo "  $0 --install-only   Only install dependencies"
    echo "  $0 --skip-deps      Skip installation, only start service"
    echo "  $0 --port 8080      Start service on port 8080"
}

# Main execution
main() {
    local install_only=false
    local skip_deps=false
    local reset_db=false
    local custom_port=""
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_help
                exit 0
                ;;
            --install-only)
                install_only=true
                shift
                ;;
            --skip-deps)
                skip_deps=true
                shift
                ;;
            --port)
                custom_port="$2"
                shift 2
                ;;
            --reset-db)
                reset_db=true
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Set custom port if provided
    if [ -n "$custom_port" ]; then
        DEFAULT_PORT=$custom_port
    fi
    
    print_success "Starting setup for $PROJECT_NAME"
    print_status "Working directory: $SCRIPT_DIR"

    # Check system prerequisites first
    if ! check_system_prerequisites; then
        print_error "System prerequisites check failed. Please resolve the issues above."
        exit 1
    fi

    # Reset database if requested
    if [ "$reset_db" = true ]; then
        print_warning "Resetting database..."
        rm -f instance/quotation.db
        print_success "Database reset completed"
    fi

    # Install dependencies unless skipped
    if [ "$skip_deps" = false ]; then
        print_status "Installing system dependencies..."

        # Install each dependency with error handling
        install_homebrew || { print_error "Homebrew installation failed"; exit 1; }
        install_python || { print_error "Python installation failed"; exit 1; }
        install_uv || print_warning "uv installation failed, will use pip instead"
        install_nodejs || print_warning "Node.js installation failed, skipping frontend setup"
        
        print_status "Setting up application environment..."
        setup_python_env || { print_error "Python environment setup failed"; exit 1; }

        # Only setup frontend if Node.js is available
        if command_exists node; then
            setup_frontend || print_warning "Frontend setup failed"
        else
            print_warning "Skipping frontend setup (Node.js not available)"
        fi

        setup_database || { print_error "Database setup failed"; exit 1; }

        # Show installation summary
        show_installation_summary

        print_success "All dependencies installed successfully!"
    fi
    
    # Start service unless install-only mode
    if [ "$install_only" = false ]; then
        echo ""
        start_service
    else
        print_success "Installation completed. Run '$0 --skip-deps' to start the service."
    fi
}

# Trap Ctrl+C and cleanup
trap 'print_warning "Service stopped by user"; exit 0' INT

# Run main function with all arguments
main "$@"
