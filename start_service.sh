#!/bin/bash

# Product Inquiry System - macOS Startup Script
# This script sets up and runs the Product Inquiry System on macOS

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="Product Inquiry System"
VENV_NAME="venv"
DEFAULT_PORT=5124
PYTHON_VERSION="3.9"

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install Homebrew if not present
install_homebrew() {
    if ! command_exists brew; then
        print_status "Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        
        # Add Homebrew to PATH for Apple Silicon Macs
        if [[ $(uname -m) == "arm64" ]]; then
            echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zshrc
            eval "$(/opt/homebrew/bin/brew shellenv)"
        fi
        print_success "Homebrew installed successfully"
    else
        print_status "Homebrew already installed"
    fi
}

# Function to install Python if not present
install_python() {
    if ! command_exists python3; then
        print_status "Installing Python..."
        brew install python@3.11
        print_success "Python installed successfully"
    else
        print_status "Python already installed: $(python3 --version)"
    fi
}

# Function to install uv if not present
install_uv() {
    if ! command_exists uv; then
        print_status "Installing uv (fast Python package installer)..."
        curl -LsSf https://astral.sh/uv/install.sh | sh
        
        # Add uv to PATH
        export PATH="$HOME/.cargo/bin:$PATH"
        echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> ~/.zshrc
        
        print_success "uv installed successfully"
    else
        print_status "uv already installed: $(uv --version)"
    fi
}

# Function to install Node.js and npm if not present
install_nodejs() {
    if ! command_exists node; then
        print_status "Installing Node.js..."
        brew install node
        print_success "Node.js installed successfully"
    else
        print_status "Node.js already installed: $(node --version)"
    fi
}

# Function to setup Python virtual environment
setup_python_env() {
    print_status "Setting up Python virtual environment..."
    
    if [ ! -d "$VENV_NAME" ]; then
        python3 -m venv "$VENV_NAME"
        print_success "Virtual environment created"
    else
        print_status "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source "$VENV_NAME/bin/activate"
    print_status "Virtual environment activated"
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install Python dependencies using uv if available, otherwise pip
    if command_exists uv; then
        print_status "Installing Python dependencies with uv..."
        uv pip install -r requirements.txt
    else
        print_status "Installing Python dependencies with pip..."
        pip install -r requirements.txt
    fi
    
    print_success "Python dependencies installed"
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."
    
    # Activate virtual environment
    source "$VENV_NAME/bin/activate"
    
    # Create instance directory if it doesn't exist
    mkdir -p instance
    
    # Initialize database if it doesn't exist
    if [ ! -f "instance/quotation.db" ]; then
        print_status "Initializing database..."
        python -c "
from app import app, db
with app.app_context():
    db.create_all()
    print('Database initialized successfully')
"
        
        # Create default users
        print_status "Creating default users..."
        python -c "
from app import app, db
from models import User
with app.app_context():
    # Create admin user
    if not User.query.filter_by(username='admin').first():
        admin = User(username='admin', is_admin=True)
        admin.set_password('szbk0755')
        db.session.add(admin)
        print('Created admin user: admin/szbk0755')
    
    # Create regular user
    if not User.query.filter_by(username='user').first():
        user = User(username='user', is_admin=False)
        user.set_password('test123')
        db.session.add(user)
        print('Created regular user: user/test123')
    
    db.session.commit()
    print('Default users created successfully')
"
        print_success "Database setup completed"
    else
        print_status "Database already exists"
    fi
}

# Function to install front-end dependencies
setup_frontend() {
    print_status "Setting up front-end dependencies..."
    
    # Check if package.json exists
    if [ -f "package.json" ]; then
        print_status "Installing npm dependencies..."
        npm install
        print_success "npm dependencies installed"
    else
        print_status "No package.json found, skipping npm dependencies"
    fi
    
    # Check for any front-end build process
    if [ -f "package.json" ] && npm run | grep -q "build"; then
        print_status "Building front-end assets..."
        npm run build
        print_success "Front-end build completed"
    fi
}

# Function to check port availability
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null; then
        return 1  # Port is in use
    else
        return 0  # Port is available
    fi
}

# Function to find available port
find_available_port() {
    local port=$DEFAULT_PORT
    while ! check_port $port; do
        print_warning "Port $port is in use, trying $((port + 1))"
        port=$((port + 1))
    done
    echo $port
}

# Function to start the service
start_service() {
    print_status "Starting $PROJECT_NAME..."
    
    # Activate virtual environment
    source "$VENV_NAME/bin/activate"
    
    # Find available port
    PORT=$(find_available_port)
    
    print_status "Starting service on port $PORT..."
    
    # Set environment variables
    export FLASK_ENV=development
    export FLASK_DEBUG=1
    export FLASK_RUN_PORT=$PORT
    
    # Start the application
    print_success "$PROJECT_NAME is starting..."
    print_success "Access the application at: http://localhost:$PORT"
    print_success "Default admin login: admin/szbk0755"
    print_success "Default user login: user/test123"
    print_success "Press Ctrl+C to stop the service"
    
    python app.py
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --help, -h          Show this help message"
    echo "  --install-only      Only install dependencies, don't start service"
    echo "  --skip-deps         Skip dependency installation, only start service"
    echo "  --port PORT         Specify port number (default: $DEFAULT_PORT)"
    echo "  --reset-db          Reset database (WARNING: This will delete all data)"
    echo ""
    echo "Examples:"
    echo "  $0                  Install dependencies and start service"
    echo "  $0 --install-only   Only install dependencies"
    echo "  $0 --skip-deps      Skip installation, only start service"
    echo "  $0 --port 8080      Start service on port 8080"
}

# Main execution
main() {
    local install_only=false
    local skip_deps=false
    local reset_db=false
    local custom_port=""
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_help
                exit 0
                ;;
            --install-only)
                install_only=true
                shift
                ;;
            --skip-deps)
                skip_deps=true
                shift
                ;;
            --port)
                custom_port="$2"
                shift 2
                ;;
            --reset-db)
                reset_db=true
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Set custom port if provided
    if [ -n "$custom_port" ]; then
        DEFAULT_PORT=$custom_port
    fi
    
    print_success "Starting setup for $PROJECT_NAME"
    print_status "Working directory: $SCRIPT_DIR"
    
    # Reset database if requested
    if [ "$reset_db" = true ]; then
        print_warning "Resetting database..."
        rm -f instance/quotation.db
        print_success "Database reset completed"
    fi
    
    # Install dependencies unless skipped
    if [ "$skip_deps" = false ]; then
        print_status "Installing system dependencies..."
        install_homebrew
        install_python
        install_uv
        install_nodejs
        
        print_status "Setting up application environment..."
        setup_python_env
        setup_frontend
        setup_database
        
        print_success "All dependencies installed successfully!"
    fi
    
    # Start service unless install-only mode
    if [ "$install_only" = false ]; then
        echo ""
        start_service
    else
        print_success "Installation completed. Run '$0 --skip-deps' to start the service."
    fi
}

# Trap Ctrl+C and cleanup
trap 'print_warning "Service stopped by user"; exit 0' INT

# Run main function with all arguments
main "$@"
