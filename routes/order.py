from flask import Blueprint, render_template, request, jsonify
from models import db, Customer, ProductInquiry, SupplierQuote, Order, Inventory
from datetime import datetime, timedelta
import logging
from flask import redirect, url_for
from flask_login import current_user
import json
from models import UserActivityLog

# 配置logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

order_bp = Blueprint('order', __name__)

@order_bp.before_request
def require_login():
    allowed_routes = ['login', 'static']
    if not current_user.is_authenticated and request.endpoint not in allowed_routes:
        return redirect(url_for('auth.login', next=request.url))

@order_bp.route('/')
def index():
    return render_template('order/index.html')

@order_bp.route('/customers')
def get_customers():
    try:
        customers = Customer.query.all()
        return jsonify([{
            'id': customer.id,
            'name': customer.name
        } for customer in customers])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@order_bp.route('/inquiries')
def get_inquiries():
    customer_id = request.args.get('customer_id', type=int)
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not all([customer_id, start_date, end_date]):
        return jsonify({'error': '缺少必要参数'}), 400

    try:
        # 将日期字符串转换为datetime对象
        start_datetime = datetime.strptime(f"{start_date} 00:00:00", '%Y-%m-%d %H:%M:%S')
        end_datetime = datetime.strptime(f"{end_date} 23:59:59", '%Y-%m-%d %H:%M:%S')
        
        # 获取指定日期范围内的询价记录及其报价
        inquiries = ProductInquiry.query.filter(
            ProductInquiry.customer_id == customer_id,
            ProductInquiry.inquiry_date >= start_datetime,
            ProductInquiry.inquiry_date <= end_datetime
        ).all()
        
        result = []
        for inquiry in inquiries:
            # 获取该询价的所有供应商报价
            supplier_quotes = SupplierQuote.query.filter_by(inquiry_id=inquiry.id).all()
            
            quotes = []
            for quote in supplier_quotes:
                # 检查该供应商报价是否已下单
                order = Order.query.filter_by(supplier_quote_id=quote.id).first()
                has_order = order is not None
                
                quotes.append({
                    'id': quote.id,
                    'supplier_name': quote.supplier.name,
                    'supplier_contact': quote.supplier.contact,
                    'price': quote.price,
                    'my_price': quote.my_price,
                    'delivery_days': quote.delivery_days,
                    'quantity': quote.quantity,
                    'has_order': has_order,
                    'order_id': order.id if order else None,
                    'order_quantity': order.quantity if order else None
                })
            
            # 检查是否有任何供应商报价已下单
            has_any_order = any(quote['has_order'] for quote in quotes)
            
            result.append({
                'id': inquiry.id,
                'product_name': inquiry.product_name,
                'brand': inquiry.brand,
                'quantity': inquiry.quantity,
                'my_price': inquiry.my_price,
                'inquiry_date': inquiry.inquiry_date.isoformat() if inquiry.inquiry_date else None,
                'expected_delivery_days': inquiry.expected_delivery_days,
                'supplier_quotes': quotes,
                'has_order': has_any_order
            })
        
        return jsonify(result)
    except ValueError as e:
        return jsonify({'error': '日期格式无效'}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@order_bp.route('/create', methods=['POST'])
def create_order():
    try:
        data = request.get_json()
        supplier_quote_id = data.get('supplier_quote_id')
        order_quantity = data.get('quantity')
        notes = data.get('notes')
        
        logger.info(f"create_order called with supplier_quote_id: {supplier_quote_id} quantity: {order_quantity}")
        
        # 验证必填字段
        if not supplier_quote_id or not order_quantity:
            missing = []
            if not supplier_quote_id: missing.append('supplier_quote_id')
            if not order_quantity: missing.append('quantity')
            return jsonify({'error': f'缺少必填字段: {", ".join(missing)}'}), 400
        
        # 获取供应商报价
        supplier_quote = SupplierQuote.query.get(supplier_quote_id)
        if not supplier_quote:
            return jsonify({'error': '供应商报价不存在'}), 404
            
        # 创建订单（使用传入的数量）
        order = Order(
            inquiry_id=supplier_quote.inquiry_id,
            supplier_quote_id=supplier_quote_id,
            supplier_quote=supplier_quote,
            quantity=order_quantity,
            status='pending',
            order_date=datetime.now(),
            notes=notes
        )
        
        db.session.add(order)
        db.session.flush()  # 获取order.id
        
        # 创建关联的库存记录
        inventory = Inventory(
            order_id=order.id,
            customer_id=supplier_quote.inquiry.customer_id,
            product_name=supplier_quote.inquiry.product_name,
            quantity=order_quantity,
            supplier_name=supplier_quote.supplier.name  # 使用正确的关系访问
        )
        
        db.session.add(inventory)
        
        # 添加审计日志
        log = UserActivityLog(
            user_id=current_user.id,
            action_type="创建订单和库存",
            details=json.dumps({
                "order_id": order.id,
                "supplier_quote_id": supplier_quote_id,
                "quantity": order_quantity,
                "inventory_id": inventory.id
            }, ensure_ascii=False),
            ip_address=request.remote_addr
        )
        db.session.add(log)
        
        db.session.commit()
        
        return jsonify({
            'message': '订单创建成功',
            'order_id': order.id,
            'inventory_id': inventory.id
        }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建订单失败: {str(e)}")
        return jsonify({'error': f'创建订单失败: {str(e)}'}), 500

@order_bp.route('/inquiry_orders/<int:inquiry_id>')
def get_inquiry_orders(inquiry_id):
    try:
        orders = Order.query.filter_by(inquiry_id=inquiry_id).order_by(Order.order_date.desc()).all()
        
        return jsonify([{
            'id': order.id,
            'order_date': order.order_date.strftime('%Y-%m-%d %H:%M:%S'),
            'supplier_name': order.supplier_name,
            'quantity': order.quantity,
            'price': order.price,
            'delivery_days': order.delivery_days,
            'status': order.status,
            'notes': order.notes
        } for order in orders])
    except Exception as e:
        logger.error(f"Error getting inquiry orders: {str(e)}")
        return jsonify({'error': str(e)}), 500

@order_bp.route('/update/<int:order_id>', methods=['PUT'])
def update_order(order_id):
    try:
        data = request.get_json()
        order = Order.query.get(order_id)
        
        if not order:
            return jsonify({'error': '订单不存在'}), 404
            
        # 更新数量
        if 'quantity' in data:
            order.quantity = int(data['quantity'])
            
        # 更新备注
        if 'notes' in data:
            order.notes = data['notes']
            
        db.session.commit()
        logger.info(f"Order {order_id} updated successfully")
        
        return jsonify({
            'message': '订单更新成功',
            'order_id': order.id,
            'quantity': order.quantity,
            'notes': order.notes
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"修改订单失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@order_bp.route('/delete/<int:order_id>', methods=['DELETE'])
def delete_order(order_id):
    try:
        order = Order.query.get(order_id)
        if not order:
            return jsonify({'error': '订单不存在'}), 404
            
        # 删除关联的库存记录
        inventory = Inventory.query.filter_by(order_id=order_id).first()
        if inventory:
            db.session.delete(inventory)
        
        # 添加审计日志
        log = UserActivityLog(
            user_id=current_user.id,
            action_type="删除订单",
            details=json.dumps({
                "order_id": order.id,
                "product_name": order.product_name,
                "supplier_name": order.supplier_name,
                "quantity": order.quantity
            }, ensure_ascii=False),
            ip_address=request.remote_addr
        )
        db.session.add(log)
        
        # 删除订单
        db.session.delete(order)
        db.session.commit()
        
        return jsonify({'message': '订单删除成功'})
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除订单失败: {str(e)}")
        return jsonify({'error': str(e)}), 500