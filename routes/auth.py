from flask import Blueprint, request, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from models import db, User
from datetime import datetime, timedelta

auth_bp = Blueprint('auth', __name__)

# 简单的登录尝试限制实现
login_attempts = {}

def check_login_attempts(username):
    """检查登录尝试次数"""
    now = datetime.now()
    if username in login_attempts:
        attempts = login_attempts[username]
        # 清理过期的尝试记录
        attempts = [t for t in attempts if t > now - timedelta(minutes=5)]
        if len(attempts) >= 5:  # 5分钟内最多5次尝试
            return False
        login_attempts[username] = attempts
    return True

def add_login_attempt(username):
    """记录登录尝试"""
    now = datetime.now()
    if username not in login_attempts:
        login_attempts[username] = []
    login_attempts[username].append(now)

@auth_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
        
    username = data.get('username', '').strip()
    password = data.get('password', '')
    
    if not username or not password:
        return jsonify({'error': 'Username and password are required'}), 400
        
    if not check_login_attempts(username):
        return jsonify({'error': 'Too many login attempts. Please try again later.'}), 429
        
    user = User.query.filter_by(username=username).first()
    
    if user and user.check_password(password):
        login_user(user)
        return jsonify({'message': 'Login successful'})
    
    add_login_attempt(username)
    return jsonify({'error': 'Invalid username or password'}), 401

@auth_bp.route('/logout', methods=['POST'])
@login_required
def logout():
    logout_user()
    return jsonify({'message': 'Logout successful'})

@auth_bp.route('/check', methods=['GET'])
def check():
    if current_user.is_authenticated:
        return jsonify({
            'logged_in': True,
            'username': current_user.username
        })
    return jsonify({'logged_in': False}) 