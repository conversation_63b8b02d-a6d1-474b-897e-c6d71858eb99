from flask import Blueprint, render_template, request, jsonify, send_file
from models import db, Customer, Order, Inventory
from datetime import datetime
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
from io import BytesIO
from num2words import num2words
from openpyxl.drawing.image import Image
import logging
from flask import redirect, url_for
from flask_login import current_user

# 配置logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

order_display_bp = Blueprint('order_display', __name__)

@order_display_bp.before_request
def require_login():
    allowed_routes = ['login', 'static']
    if not current_user.is_authenticated and request.endpoint not in allowed_routes:
        return redirect(url_for('auth.login', next=request.url))

@order_display_bp.route('/')
def index():
    return render_template('order_display/index.html')

@order_display_bp.route('/customers')
def get_customers():
    customers = Customer.query.order_by(Customer.name).all()
    return jsonify([{
        'id': customer.id,
        'name': customer.name
    } for customer in customers])

@order_display_bp.route('/orders')
def get_orders():
    customer_id = request.args.get('customer_id', type=int)
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    logger.info(f"get_orders called with params: customer_id={customer_id}, start_date={start_date}, end_date={end_date}")
    
    if not all([customer_id, start_date, end_date]):
        logger.error("Missing required parameters")
        return jsonify({'error': '请提供客户ID和日期范围'}), 400
        
    try:
        # 将日期字符串转换为本地datetime对象
        start_datetime = datetime.strptime(f"{start_date} 00:00:00", '%Y-%m-%d %H:%M:%S')
        end_datetime = datetime.strptime(f"{end_date} 23:59:59", '%Y-%m-%d %H:%M:%S')
        
        logger.info(f"Using local date range: start={start_datetime}, end={end_datetime}")
        
        # 获取指定日期范围内的订单
        orders = Order.query.filter(
            Order.customer_id == customer_id,
            Order.order_date >= start_datetime,
            Order.order_date <= end_datetime
        ).order_by(Order.order_date.desc()).all()
        
        logger.info(f"Found {len(orders)} orders in date range")
        
        results = []
        for order in orders:
            # 使用本地时间格式化日期
            order_data = {
                'id': order.id,
                'order_date': order.order_date.strftime('%Y-%m-%d %H:%M:%S'),
                'product_name': order.product_name,
                'brand': order.brand,
                'quantity': order.quantity,
                'price': order.price,
                'my_price': order.my_price,
                'supplier_name': order.supplier_name,
                'delivery_days': order.delivery_days,
                'expected_delivery_date': order.expected_delivery_date.strftime('%Y-%m-%d') if order.expected_delivery_date else None,
                'status': order.status,
                'notes': order.notes
            }
            results.append(order_data)
            logger.debug(f"Processing order {order.id} with local date {order_data['order_date']}")
        
        return jsonify(results)
    except ValueError as e:
        logger.error(f"Date parsing error: {str(e)}")
        return jsonify({'error': '日期格式无效'}), 400
    except Exception as e:
        logger.error(f"Unexpected error in get_orders: {str(e)}")
        return jsonify({'error': '处理请求时发生错误'}), 500

@order_display_bp.route('/orders/<int:order_id>/status', methods=['PUT'])
def update_order_status(order_id):
    try:
        data = request.get_json()
        new_status = data.get('status')
        notes = data.get('notes', '')
        
        if not new_status:
            return jsonify({'error': '缺少状态参数'}), 400
            
        valid_statuses = ['pending', 'confirmed', 'shipped', 'completed', 'cancelled']
        if new_status not in valid_statuses:
            return jsonify({'error': '无效的状态值'}), 400
            
        order = Order.query.get(order_id)
        if not order:
            return jsonify({'error': '订单不存在'}), 404
            
        # 如果订单状态变更为已确认,创建库存记录
        if new_status == 'confirmed' and order.status != 'confirmed':
            # 检查是否已存在库存记录
            inventory = Inventory.query.filter_by(order_id=order_id).first()
            if not inventory:
                inventory = Inventory(
                    order_id=order_id,
                    customer_id=order.customer_id,
                    product_name=order.product_name,
                    quantity=order.quantity,
                    supplier_name=order.supplier_name
                )
                db.session.add(inventory)
            
        order.status = new_status
        if notes:
            order.notes = notes
            
        db.session.commit()
        
        return jsonify({
            'message': '更新成功',
            'status': new_status,
            'notes': notes
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@order_display_bp.route('/download')
def download_excel():
    customer_id = request.args.get('customer_id', type=int)
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    exchange_rate = float(request.args.get('exchange_rate', 7.32))
    logger.info(f"download_excel called with params: customer_id={customer_id}, start_date={start_date}, end_date={end_date}, exchange_rate={exchange_rate}")
    
    if not all([customer_id, start_date, end_date]):
        logger.error("Missing required parameters for Excel download")
        return jsonify({'error': '请提供客户ID和日期范围'}), 400
        
    customer = Customer.query.get(customer_id)
    if not customer:
        logger.error(f"Customer not found: {customer_id}")
        return jsonify({'error': '客户不存在'}), 404
        
    try:
        # 将日期字符串转换为本地datetime对象
        start_datetime = datetime.strptime(f"{start_date} 00:00:00", '%Y-%m-%d %H:%M:%S')
        end_datetime = datetime.strptime(f"{end_date} 23:59:59", '%Y-%m-%d %H:%M:%S')
        
        logger.info(f"Using local date range for Excel: start={start_datetime}, end={end_datetime}")
        
        # 获取指定日期范围内的已完成订单
        orders = Order.query.filter(
            Order.customer_id == customer_id,
            Order.order_date >= start_datetime,
            Order.order_date <= end_datetime,
            Order.status == 'completed'
        ).order_by(Order.order_date.asc()).all()
        
        order_count = len(orders)
        logger.info(f"Found {order_count} completed orders for Excel download")

        if not orders:
            logger.warning("No completed orders found in date range")
            return jsonify({'error': '没有找到已完成的订单'}), 404

        # 使用第一个订单的本地日期作为发票日期
        invoice_date = orders[0].order_date.strftime('%Y.%m.%d')
        invoice_no = f"SE{orders[0].order_date.strftime('%Y%m%d')}"
        
        # 创建Excel工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        
        # 设置列宽
        ws.column_dimensions['A'].width = 8  # ITEM
        ws.column_dimensions['B'].width = 50  # MODEL
        ws.column_dimensions['C'].width = 15  # QTY
        ws.column_dimensions['D'].width = 15  # Exchange rate
        ws.column_dimensions['E'].width = 15  # PRICE(RMB)
        ws.column_dimensions['F'].width = 15  # PRICE(USD)
        ws.column_dimensions['G'].width = 28  # AMOUNT

        # 设置默认行高
        ws.sheet_format.defaultRowHeight = 15.0

        # 公司抬头
        ws.merge_cells('A1:G1')
        ws['A1'] = 'SHENZHEN BEIKOI TECHNOLOGY COMPANY LIMITED'
        ws['A1'].font = Font(name='Times New Roman', size=14, bold=True)
        ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
        ws.row_dimensions[1].height = 18

        # 空行
        ws.row_dimensions[2].height = 16
        ws.row_dimensions[3].height = 16

        # Proforma Invoice
        ws.merge_cells('A4:G4')
        ws['A4'] = 'Proforma Invoice'
        ws['A4'].font = Font(name='Times New Roman', size=16, bold=True)
        ws['A4'].alignment = Alignment(horizontal='center', vertical='center')
        ws.row_dimensions[4].height = 20

        # 客户信息
        ws.merge_cells('A5:A6')
        ws['A5'] = 'Messers:'
        ws.merge_cells('B5:B6')
        ws['B5'] = customer.name
        ws['B5'].font = Font(name='Times New Roman', size=12)
        ws['B5'].alignment = Alignment(horizontal='left', vertical='center')
        ws['F5'] = 'Invoice NO:'
        ws['G5'] = invoice_no
        ws['G5'].font = Font(name='Times New Roman', size=12)
        ws.row_dimensions[5].height = 16

        ws['F6'] = 'Date:'
        ws['G6'] = invoice_date
        ws['G6'].font = Font(name='Times New Roman', size=12)
        ws.row_dimensions[6].height = 16

        ws.merge_cells('A7:A8')  # 合并A7和A8单元格
        ws['A7'] = 'Address:'
        ws.merge_cells('B7:B8')  # 合并B7和B8单元格
        ws['B7'] = customer.address
        ws['B7'].font = Font(name='Times New Roman', size=12)
        ws['B7'].alignment = Alignment(horizontal='left', wrap_text=True, vertical='top')
        ws['F8'] = 'From:'
        ws['G8'] = 'Shenzhen,China'
        ws['G8'].font = Font(name='Times New Roman', size=12)
        ws.row_dimensions[8].height = 16  # 增加行高
        ws.row_dimensions[9].height = 16  # 增加行高

        ws['F9'] = 'To:'
        ws['G9'] = customer.country
        ws['G9'].font = Font(name='Times New Roman', size=12)

        ws['A9'] = 'TEL:'
        ws['B9'] = customer.phone
        ws['B9'].font = Font(name='Times New Roman', size=12)
        ws['F10'] = 'PO:'
        ws['G10'] = 'N/A'
        ws['G10'].font = Font(name='Times New Roman', size=12)

        ws['A10'] = 'FAX:'
        ws.row_dimensions[10].height = 16

        ws['A11'] = 'ATTN:'
        ws['A11'].font = Font(name='Times New Roman', size=12)
        ws['B11'] = customer.contact if customer else ''
        ws['B11'].font = Font(name='Times New Roman', size=12)

        # 设置客户信息区域字体
        for row in range(5, 11):
            for col in ['A', 'F']:
                cell = ws[f'{col}{row}']
                cell.font = Font(name='Times New Roman', size=12)
                cell.alignment = Alignment(horizontal='left', vertical='center')

        # 表头
        headers = ['ITEM', 'MODEL', 'QTY', 'Exchange rate', 'PRICE(RMB)', 'PRICE(USD)', 'AMOUNT']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=12, column=col)
            cell.value = header
            cell.font = Font(name='Times New Roman', size=12)
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
        ws.row_dimensions[12].height = 16

        # 数据行
        total_amount = 0
        for idx, order in enumerate(orders, 1):
            row = idx + 12
            price_usd = float(order.my_price) / exchange_rate
            amount = price_usd * order.quantity
            total_amount += amount

            # 设置行高
            ws.row_dimensions[row].height = 16

            # 写入数据
            cells = [
                (1, idx, 'center'),
                (2, order.product_name, 'left'),  # MODEL列加粗
                (3, order.quantity, 'center'),    # QTY列加粗
                (4, exchange_rate, 'center'),
                (5, order.my_price, 'center'),        # PRICE(RMB)列加粗
                (6, f"{price_usd:.2f}", 'center'), # PRICE(USD)列加粗
                (7, f"{amount:.2f}", 'center')     # AMOUNT列加粗
            ]

            for col, value, align in cells:
                cell = ws.cell(row=row, column=col)
                cell.value = value
                cell.font = Font(name='Times New Roman', size=12, bold=True)
                cell.alignment = Alignment(horizontal=align, vertical='center')
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

        # 合计行
        total_row = len(orders) + 13
        ws.row_dimensions[total_row].height = 16

        # Total文本
        total_cell = ws.cell(row=total_row, column=1)
        total_cell.value = 'Total'
        total_cell.font = Font(name='Times New Roman', size=12, bold=True)
        total_cell.alignment = Alignment(horizontal='center', vertical='center')

        # 合计数量
        qty_total_cell = ws.cell(row=total_row, column=3)
        qty_total_cell.value = sum(order.quantity for order in orders)
        qty_total_cell.font = Font(name='Times New Roman', size=12, bold=True)
        qty_total_cell.alignment = Alignment(horizontal='center', vertical='center')

        # 合计金额
        amount_total_cell = ws.cell(row=total_row, column=7)
        amount_total_cell.value = f"${total_amount:.2f}"
        amount_total_cell.font = Font(name='Times New Roman', size=12, bold=True, underline='single')
        amount_total_cell.alignment = Alignment(horizontal='center', vertical='center')

        # 为合计行添加边框和设置其他单元格格式
        for col in range(1, 8):
            cell = ws.cell(row=total_row, column=col)
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            # 设置Exchange rate单元格格式
            if col == 4:
                cell.font = Font(name='Times New Roman', size=12, bold=True)
                cell.alignment = Alignment(horizontal='center', vertical='center')

        # 金额大写
        words_row = total_row + 1
        ws.merge_cells(f'A{words_row}:G{words_row}')
        # 获取小数点后的数字并转换为单词
        decimal_part = str(round((total_amount % 1) * 100)).zfill(2)
        decimal_words = ' '.join(num2words(int(d)).upper() for d in decimal_part)
        ws[f'A{words_row}'] = f"TOTAL SAY US DOLLAR {num2words(int(total_amount)).upper()} POINT {decimal_words} ONLY***"
        ws[f'A{words_row}'].font = Font(name='Times New Roman', size=14, bold=True)
        ws[f'A{words_row}'].alignment = Alignment(horizontal='center', vertical='center')
        ws.row_dimensions[words_row].height = 16

        # 空行
        ws.row_dimensions[words_row + 1].height = 16
        ws.row_dimensions[words_row + 2].height = 16

        # 付款信息
        payment_info_row = words_row + 3
        payment_info = [
            '1. Payment Term: 100% payment',
            "2. Bank's information:",
            "    Account Holder's Name: Shenzhen Beikoi Technology Company Limited",
            "    Account Number: **********",
            "    Bank's Name: DBS Bank (Hong Kong) Limited",
            "    Bank's Address: 11th Floor, The Center, 99 Queen's Road Central, Central, Hong Kong",
            "    SWIFT Code: DHBKHKHH (DHBKHKHHXXX * If 11 characters are required)",
            "    Bank Code: 016",
            "Branch Code: 478 * If paying from Hong Kong banks"
        ]
        company_row = payment_info_row
        for i, info in enumerate(payment_info):
            row = payment_info_row + i
            cell = ws[f'A{row}']
            cell.value = info
            
            # 为Bank's information添加下划线
            if i == 1:  # Bank's information行
                cell.font = Font(name='Times New Roman', size=12, underline='single', bold=True)
            else:
                cell.font = Font(name='Times New Roman', size=12, bold=True)
                
            cell.alignment = Alignment(horizontal='left', vertical='center')
            ws.row_dimensions[row].height = 16
            
            # 前两行合并单元格
            if i < 2:
                ws.merge_cells(f'A{row}:G{row}')
            company_row = row
        company_row += 2
        ws.row_dimensions[company_row].height = 16
        logo = Image('sign.png')
        logo.width = 100
        logo.height = 50
        ws.add_image(logo, f'C{company_row}')

        # 保存到内存中
        excel_file = BytesIO()
        wb.save(excel_file)
        excel_file.seek(0)
        
        return send_file(
            excel_file,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'Invoice-{invoice_no}.xlsx'
        )
    except ValueError:
        return jsonify({'error': '日期格式无效'}), 400

@order_display_bp.route('/orders/<int:order_id>/delivery-date', methods=['PUT'])
def update_delivery_date(order_id):
    try:
        data = request.get_json()
        new_date = data.get('expected_delivery_date')
        
        if not new_date:
            return jsonify({'error': '缺少预计交货日期参数'}), 400
            
        order = Order.query.get(order_id)
        if not order:
            return jsonify({'error': '订单不存在'}), 404
            
        # 更新预计交货日期
        order.expected_delivery_date = datetime.strptime(new_date, '%Y-%m-%d')
        
        # 如果订单状态为已确认且没有对应的库存记录,创建库存记录
        if order.status == 'confirmed':
            inventory = Inventory.query.filter_by(order_id=order_id).first()
            if not inventory:
                inventory = Inventory(
                    order_id=order_id,
                    customer_id=order.customer_id,
                    product_name=order.product_name,
                    quantity=order.quantity,
                    supplier_name=order.supplier_name
                )
                db.session.add(inventory)
            
        db.session.commit()
        
        return jsonify({
            'message': '预计交货日期更新成功',
            'expected_delivery_date': new_date
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def get_status_text(status):
    status_map = {
        'pending': '待处理',
        'confirmed': '已确认',
        'shipped': '已发货',
        'completed': '已完成',
        'cancelled': '已取消'
    }
    return status_map.get(status, '未知状态')