from flask import Blueprint, render_template, request, jsonify, send_file, redirect, url_for
from models import Customer, ProductInquiry, SupplierQuote, db
from datetime import datetime
import openpyxl
from openpyxl.styles import Font, Alignment
from io import BytesIO
import os
from flask_login import current_user, login_required

inquiry_display_bp = Blueprint('inquiry_display', __name__)

@inquiry_display_bp.before_request
def require_login():
    allowed_routes = ['login', 'static']
    if not current_user.is_authenticated and request.endpoint not in allowed_routes:
        return redirect(url_for('auth.login', next=request.url))

@inquiry_display_bp.route('/')
def index():
    return render_template('inquiry_display/index.html')

@inquiry_display_bp.route('/customers')
def get_customers():
    customers = Customer.query.order_by(Customer.name).all()
    return jsonify([{
        'id': customer.id,
        'name': customer.name
    } for customer in customers])

@inquiry_display_bp.route('/inquiries')
def get_inquiries():
    customer_id = request.args.get('customer_id', type=int)
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not all([customer_id, start_date, end_date]):
        return jsonify({'error': '请提供客户ID和日期范围'}), 400
        
    try:
        # 将日期字符串转换为datetime对象
        start_date = datetime.strptime(start_date, '%Y-%m-%d')
        end_date = datetime.strptime(end_date, '%Y-%m-%d')
        # 将结束日期调整到当天的最后一刻
        end_date = end_date.replace(hour=23, minute=59, second=59)
        
        # 获取指定日期范围内的记录
        inquiries = ProductInquiry.query.filter(
            ProductInquiry.customer_id == customer_id,
            ProductInquiry.inquiry_date >= start_date,
            ProductInquiry.inquiry_date <= end_date
        ).order_by(ProductInquiry.inquiry_date.asc()).all()
        
        results = []
        for inquiry in inquiries:
            supplier_quotes = [{
                'supplier_name': quote.supplier.name,
                'supplier_contact': quote.supplier.contact,
                'price': quote.price,
                'delivery_days': quote.delivery_days
            } for quote in inquiry.supplier_quotes]
            
            results.append({
                'id': inquiry.id,
                'product_name': inquiry.product_name,
                'quantity': inquiry.quantity,
                'expected_delivery_days': inquiry.expected_delivery_days,
                'my_price': inquiry.my_price,
                'inquiry_date': inquiry.inquiry_date.strftime('%Y-%m-%d'),
                'supplier_quotes': supplier_quotes
            })
        
        return jsonify(results)
    except ValueError:
        return jsonify({'error': '日期格式无效'}), 400

@inquiry_display_bp.route('/download')
def download_excel():
    customer_id = request.args.get('customer_id', type=int)
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if not all([customer_id, start_date, end_date]):
        return jsonify({'error': '请提供客户ID和日期范围'}), 400
        
    customer = Customer.query.get(customer_id)
    if not customer:
        return jsonify({'error': '客户不存在'}), 404
        
    try:
        # 将日期字符串转换为datetime对象
        start_date = datetime.strptime(start_date, '%Y-%m-%d')
        end_date = datetime.strptime(end_date, '%Y-%m-%d')
        # 将结束日期调整到当天的最后一刻
        end_date = end_date.replace(hour=23, minute=59, second=59)
        
        # 获取指定日期范围内的记录
        inquiries = ProductInquiry.query.filter(
            ProductInquiry.customer_id == customer_id,
            ProductInquiry.inquiry_date >= start_date,
            ProductInquiry.inquiry_date <= end_date
        ).order_by(ProductInquiry.inquiry_date.asc()).all()
        
        # 创建Excel工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "产品咨询记录"
        
        # 设置表头
        headers = ['日期', '产品名称', '数量', '预计交货天数', '我的报价']
        # 动态添加供应商列
        max_suppliers = max([len(inq.supplier_quotes) for inq in inquiries]) if inquiries else 0
        for i in range(max_suppliers):
            headers.extend([f'供应商{i+1}', f'联系方式{i+1}', f'报价{i+1}', f'交货周期{i+1}'])
        
        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
        
        # 写入数据
        for row, inquiry in enumerate(inquiries, 2):
            col = 1
            # 基本信息
            ws.cell(row=row, column=col).value = inquiry.inquiry_date.strftime('%Y-%m-%d'); col += 1
            ws.cell(row=row, column=col).value = inquiry.product_name; col += 1
            ws.cell(row=row, column=col).value = inquiry.quantity; col += 1
            ws.cell(row=row, column=col).value = inquiry.expected_delivery_days; col += 1
            ws.cell(row=row, column=col).value = inquiry.my_price; col += 1
            
            # 供应商报价
            for quote in inquiry.supplier_quotes:
                ws.cell(row=row, column=col).value = quote.supplier.name; col += 1
                ws.cell(row=row, column=col).value = quote.supplier.contact; col += 1
                ws.cell(row=row, column=col).value = quote.price; col += 1
                ws.cell(row=row, column=col).value = quote.delivery_days; col += 1
                
            # 填充空白供应商列
            while col <= len(headers):
                ws.cell(row=row, column=col).value = ''; col += 1
        
        # 调整列宽
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width
        
        # 保存到内存中
        excel_file = BytesIO()
        wb.save(excel_file)
        excel_file.seek(0)
        
        return send_file(
            excel_file,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'{customer.name}-产品咨询记录-{start_date.strftime("%Y%m%d")}-{end_date.strftime("%Y%m%d")}.xlsx'
        )
    except ValueError:
        return jsonify({'error': '日期格式无效'}), 400

@inquiry_display_bp.route('/inquiries/<int:inquiry_id>/update_my_price', methods=['PUT'])
@login_required
def update_my_price(inquiry_id):
    data = request.get_json()
    if 'my_price' not in data:
        return jsonify({'error': '缺少my_price参数'}), 400
    
    inquiry = ProductInquiry.query.get_or_404(inquiry_id)
    
    try:
        # 更新询价记录的报价
        inquiry.my_price = float(data['my_price'])
        
        # 更新关联的供应商报价记录
        SupplierQuote.query.filter_by(inquiry_id=inquiry_id).update({
            'my_price': inquiry.my_price
        })
        
        db.session.commit()
        
        return jsonify({
            'message': '报价更新成功',
            'new_price': inquiry.my_price
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500 
    
@inquiry_display_bp.route('/inquiries/<int:inquiry_id>/update_delivery_days', methods=['PUT'])
@login_required
def update_delivery_days(inquiry_id):
    data = request.get_json()
    if 'delivery_days' not in data:
        return jsonify({'error': '缺少delivery_days参数'}), 400

    inquiry = ProductInquiry.query.get_or_404(inquiry_id)
    
    try:
        # 更新询价记录的交货周期
        inquiry.expected_delivery_days = int(data['delivery_days'])
        db.session.commit()
        return jsonify({
            'message': '交货周期更新成功',
            'new_delivery_days': inquiry.expected_delivery_days
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500 

