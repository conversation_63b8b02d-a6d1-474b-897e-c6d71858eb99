from io import BytesIO
from flask import Blueprint, render_template, send_file
from flask_login import login_required
from openpyxl import Workbook
from datetime import datetime
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
from models import Inventory, Order, db
import logging

outbound_bp = Blueprint('outbound', __name__)

@outbound_bp.route('/')
@login_required
def index():
    return render_template('outbound/index.html')

@outbound_bp.route('/download/<customer_id>')
@login_required
def download(customer_id):
    # 执行查询并按产品名称分组
    query = db.session.query(
        Order.order_date.label('order_date'),
        Inventory.product_name,
        Inventory.quantity.label('quantity'),
        Order.my_price.label('my_price'),
        (Order.my_price * Inventory.quantity).label('order_amount'),  # 单个订单金额
        Inventory.shipping_date.label('shipping_date'),
        Inventory.shipping_method.label('shipping_method'),
        Order.id.label('order_id')  # 添加订单ID以区分不同订单
    ).join(
        Order, Inventory.order_id == Order.id
    ).filter(
        Order.customer_id == customer_id
    ).order_by(
        Order.order_date.asc(),  # 按订单日期排序
        Inventory.shipping_date.asc()
    )

    orders = query.all()
    
    # 创建Excel文件
    headers = ["订货日期(order date)", "品名(item)", "数量(Quantity)", "单价(price)/元", "总额(Total amount)", 
              "深圳/到达日期(Arrival date)", "发送(飞机/航运)", "到达韩国日", "到达韩国的数量", "交割日"]
    korean_headers = ["주문일자", "품명", "수량", "단가/위안", "총액", "심천 도착일", "항공/해운", 
                     "한국도착일", "한국도착수량", "결제일"]
    
    wb = Workbook()
    ws = wb.active
    ws.title = "出库清单"

    header_font = Font(bold=True, name='Times New Roman')
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    border_style = Side(border_style='thin')
    border = Border(left=border_style, right=border_style, top=border_style, bottom=border_style)
    
    # 添加表头
    ws.append(headers)
    for cell in ws[1]:
        cell.font = header_font
        cell.alignment = header_alignment
        cell.border = border
    ws.append(korean_headers)
    for cell in ws[2]:
        cell.font = header_font
        cell.alignment = header_alignment
        cell.border = border
    
    # 创建黄色填充样式
    yellow_fill = PatternFill(start_color='FFFF00', end_color='FFFF00', fill_type='solid')
    ws['E1'].fill = yellow_fill
    # 添加数据行并设置样式
    for order in orders:
        row = [
            order.order_date.strftime('%Y.%m.%d') if order.order_date else '',
            order.product_name,
            f"{order.quantity:,} EA",
            order.my_price,
            order.order_amount,
            '',
            '飞机' if order.shipping_method == 'air' else '航运' if order.shipping_method == 'sea' else order.shipping_method,
            '',  # 到达韩国日
            f"{order.quantity:,} EA",  # 到达韩国的数量
            order.shipping_date.strftime('%Y.%m.%d') if order.shipping_date else ''
        ]
        ws.append(row)
        last_row = ws.max_row
        for cell in ws[last_row]:
            # 只有当有发货日期时才标黄
            if order.shipping_date is not None and order.shipping_method is not None:
                cell.fill = yellow_fill
            cell.font = Font(name='Times New Roman', size=11)
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            cell.border = border
    
    columns_to_adjust = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']
    for col in columns_to_adjust:
        ws.column_dimensions[col].width = 20
    ws.freeze_panes = 'A3'

    excel_file = BytesIO()
    wb.save(excel_file)
    excel_file.seek(0)
    
    return send_file(
        excel_file,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=f'出库清单_{datetime.now().strftime("%Y%m%d")}.xlsx'
    ) 