from flask import Blueprint, render_template, request, jsonify
from models import db, Customer, ProductInquiry, SupplierQuote, Supplier, UserActivityLog, SerialNumber
from datetime import datetime
import re
from flask_login import current_user, login_required
import json

inquiry_bp = Blueprint('inquiry', __name__)

@inquiry_bp.route('/')
@login_required
def index():
    is_admin = False
    if hasattr(current_user, 'is_admin'):
        is_admin = current_user.is_admin
    return render_template('inquiry/index.html', is_admin=is_admin)

@inquiry_bp.route('/manage-quotes')
@login_required
def manage_quotes():
    return render_template('inquiry/manage_quotes.html')

@inquiry_bp.route('/customers')
def get_customers():
    customers = Customer.query.all()
    return jsonify([{'id': c.id, 'name': c.name} for c in customers])

@inquiry_bp.route('/customers', methods=['POST'])
def create_customer():
    data = request.get_json()
    customer = Customer(name=data['name'])
    db.session.add(customer)
    db.session.commit()
    return jsonify({'id': customer.id, 'name': customer.name})

@inquiry_bp.route('/search')
def search_products():
    query = request.args.get('query', '')
    if not query:
        return jsonify([])
    
    pattern = re.compile(f'^{re.escape(query)}.*', re.IGNORECASE)
    results = []
    
    # 搜索产品咨询记录
    inquiries = ProductInquiry.query.all()
    for inquiry in inquiries:
        if pattern.match(inquiry.product_name):
            # 获取该产品的所有供应商报价
            supplier_quotes = SupplierQuote.query.filter_by(inquiry_id=inquiry.id).all()
            quotes = [{
                'supplier_name': quote.supplier.name,
                'supplier_contact': quote.supplier.contact,
                'price': quote.price
            } for quote in supplier_quotes]
            
            results.append({
                'product_name': inquiry.product_name,
                'quantity': inquiry.quantity,
                'expected_delivery_days': inquiry.expected_delivery_days,
                'my_price': inquiry.my_price,
                'supplier_quotes': quotes,
                'inquiry_date': inquiry.inquiry_date.strftime('%Y-%m-%d')
            })
    
    return jsonify(results)

@inquiry_bp.route('/submit', methods=['POST'])
def submit_inquiry():
    data = request.get_json()
    
    # 验证必填字段
    required_fields = ['customer_id', 'products']
    if not all(field in data for field in required_fields):
        return jsonify({'error': '缺少必填字段'}), 400
    
    # 验证产品列表不为空
    if not data['products']:
        return jsonify({'error': '至少需要一个产品'}), 400
    
    try:
        # 创建产品咨询记录
        for product in data['products']:
            inquiry = ProductInquiry(
                customer_id=data['customer_id'],
                product_name=product['product_name'],
                quantity=product['quantity'],
                expected_delivery_days=product['expected_delivery_days'],
                my_price=product['my_price'],
                inquiry_date=datetime.date()
            )
            db.session.add(inquiry)
            db.session.flush()  # 获取inquiry_id

            # 创建序列号记录
            for serial_number in product.get('serial_numbers', []):
                if serial_number.strip():  # 只保存非空序列号
                    serial_record = SerialNumber(
                        inquiry_id=inquiry.id,
                        serial_number=serial_number.strip()
                    )
                    db.session.add(serial_record)

            # 创建供应商报价记录
            for quote in product['supplier_quotes']:
                # 查找或创建供应商
                supplier = Supplier.query.filter_by(name=quote['supplier_name']).first()
                if not supplier:
                    supplier = Supplier(
                        name=quote['supplier_name'],
                        contact=quote.get('supplier_contact', '')
                    )
                    db.session.add(supplier)
                    db.session.flush()
                
                supplier_quote = SupplierQuote(
                    inquiry_id=inquiry.id,
                    supplier_id=supplier.id,
                    price=float(quote['price'])
                )
                db.session.add(supplier_quote)
                
                # 记录供应商操作
                supplier_log = UserActivityLog(
                    user_id=current_user.id,
                    action_type="保存供应商",
                    details=json.dumps({
                        "supplier_name": supplier.name,
                        "inquiry_id": inquiry.id
                    }),
                    ip_address=request.remote_addr
                )
                db.session.add(supplier_log)
        
        db.session.commit()
        return jsonify({'message': 'Inquiry submitted successfully'}), 201
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@inquiry_bp.route('/suppliers', methods=['POST'])
@login_required
def save_supplier():
    data = request.json
    if not data or 'name' not in data:
        return jsonify({"error": "Supplier name is required"}), 400
    
    if not data.get('product_name'):
        return jsonify({"error": "Product name is required"}), 400
        
    if not data.get('customer_id'):
        return jsonify({"error": "Customer ID is required"}), 400
        
    # 验证必填字段
    required_fields = ['product_name', 'quantity', 'price', 'delivery_days', 'my_price', 'customer_id']
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return jsonify({"error": f"Missing required fields: {', '.join(missing_fields)}"}), 400
        
    try:
        # 检查是否已存在相同名称的供应商
        existing_supplier = Supplier.query.filter_by(name=data['name']).first()
        if existing_supplier:
            # 更新现有供应商信息
            existing_supplier.contact = data.get('contact', '')
            existing_supplier.default_delivery_days = data.get('default_delivery_days', 0)
            supplier = existing_supplier
        else:
            # 创建新供应商
            supplier = Supplier(
                name=data['name'],
                contact=data.get('contact', ''),
                default_delivery_days=data.get('default_delivery_days', 0)
            )
            db.session.add(supplier)
        
        db.session.flush()
        
        try:
            inquiry_date = datetime.strptime(data.get('inquiry_date', datetime.now().strftime('%Y-%m-%d')), '%Y-%m-%d').date()
        except ValueError:
            inquiry_date = datetime.now().date()
            
        # 查找或创建产品询价记录
        inquiry = ProductInquiry.query.filter_by(
            product_name=data['product_name'],
            customer_id=data['customer_id']  # 添加客户ID条件
        ).order_by(ProductInquiry.inquiry_date.desc()).first()
        
        if not inquiry:
            # 如果没有现有询价，创建一个新的
            inquiry = ProductInquiry(
                customer_id=data['customer_id'],  # 使用请求中的客户ID
                product_name=data['product_name'],
                quantity=int(data['quantity']),
                expected_delivery_days=int(data['delivery_days']),
                my_price=float(data['my_price']),
                brand=data.get('brand', ''),
                inquiry_date=inquiry_date
            )
            db.session.add(inquiry)
            db.session.flush()  # 获取inquiry.id
        else:
            # 更新现有询价
            inquiry.quantity = int(data['quantity'])
            inquiry.expected_delivery_days = int(data['delivery_days'])
            inquiry.my_price = float(data['my_price'])
            inquiry.brand = data.get('brand', '')
            inquiry.inquiry_date = inquiry_date
            
        # 查找是否已存在该供应商对该产品的报价
        existing_quote = SupplierQuote.query.filter_by(
            supplier_id=supplier.id,
            inquiry_id=inquiry.id
        ).first()
        
        if existing_quote:
            # 更新现有报价
            existing_quote.price = float(data.get('price', 0))
            existing_quote.delivery_days = int(data.get('delivery_days', 0))
            existing_quote.brand = data.get('brand', '')
            existing_quote.quantity = int(data.get('quantity', 0))
            existing_quote.my_price = float(data.get('my_price', 0))
            existing_quote.inquiry_date = inquiry_date
        else:
            # 创建新的报价记录
            supplier_quote = SupplierQuote(
                inquiry_id=inquiry.id,
                supplier_id=supplier.id,
                price=float(data.get('price', 0)),
                delivery_days=int(data.get('delivery_days', 0)),
                product_name=data['product_name'],
                brand=data.get('brand', ''),
                quantity=int(data.get('quantity', 0)),
                my_price=float(data.get('my_price', 0)),
                inquiry_date=inquiry_date
            )
            db.session.add(supplier_quote)
        
        db.session.commit()
        
        # 查找该供应商对该产品的所有历史报价
        historical_quotes = SupplierQuote.query.filter_by(
            supplier_id=supplier.id,
            product_name=data['product_name']
        ).order_by(SupplierQuote.inquiry_date.desc()).all()
        
        # 添加审计日志
        log = UserActivityLog(
            user_id=current_user.id,
            action_type="保存供应商",
            details=json.dumps({
                "brand": data.get('brand', ''),
                "product_name": data['product_name'],
                "supplier_name": supplier.name,
                "quantity": data['quantity'],
                "price": data['price'],
                "delivery_days": data['delivery_days'],
                "inquiry_date": inquiry_date.isoformat(),
                "customer_id": data['customer_id']  # 添加客户ID到日志
            }),
            ip_address=request.remote_addr
        )
        db.session.add(log)
        db.session.commit()
        
        return jsonify({
            'name': supplier.name,
            'contact': supplier.contact,
            'default_delivery_days': supplier.default_delivery_days,
            'historical_quotes': [{
                'price': quote.price,
                'delivery_days': quote.delivery_days,
                'inquiry_date': quote.inquiry_date.isoformat(),
                'product_name': quote.product_name,
                'brand': quote.brand,
                'quantity': quote.quantity,
                'supplier_contact': supplier.contact
            } for quote in historical_quotes],
            'message': 'Supplier and quote saved'
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"Error saving supplier and quote: {str(e)}")
        return jsonify({"error": "Failed to save supplier and quote"}), 500

@inquiry_bp.route('/supplier-quote/<int:quote_id>', methods=['DELETE'])
@login_required
def delete_supplier_quote(quote_id):
    """删除供应商报价"""
    try:
        # 查找报价记录
        quote = SupplierQuote.query.get(quote_id)
        if not quote:
            return jsonify({'error': '报价记录不存在'}), 404

        # 检查是否有关联的订单
        if quote.orders:
            return jsonify({'error': '该报价已有关联订单，无法删除'}), 400

        # 记录删除操作
        log = UserActivityLog(
            user_id=current_user.id,
            action_type="删除供应商报价",
            details=json.dumps({
                "quote_id": quote.id,
                "supplier_name": quote.supplier.name,
                "product_name": quote.product_name,
                "price": quote.price,
                "inquiry_id": quote.inquiry_id
            }),
            ip_address=request.remote_addr
        )
        db.session.add(log)

        # 删除报价
        db.session.delete(quote)
        db.session.commit()

        return jsonify({'message': '供应商报价删除成功'})

    except Exception as e:
        db.session.rollback()
        print(f"Error deleting supplier quote: {str(e)}")
        return jsonify({"error": "删除失败，请重试"}), 500

@inquiry_bp.route('/supplier-quotes', methods=['GET'])
@login_required
def get_supplier_quotes():
    """获取所有供应商报价列表，用于管理"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        product_name = request.args.get('product_name', '').strip()
        supplier_name = request.args.get('supplier_name', '').strip()

        # 构建查询
        query = SupplierQuote.query.join(Supplier).join(ProductInquiry)

        # 添加过滤条件
        if product_name:
            query = query.filter(SupplierQuote.product_name.ilike(f'%{product_name}%'))
        if supplier_name:
            query = query.filter(Supplier.name.ilike(f'%{supplier_name}%'))

        # 按日期降序排列
        query = query.order_by(SupplierQuote.inquiry_date.desc())

        # 分页
        quotes = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'quotes': [{
                'id': quote.id,
                'product_name': quote.product_name,
                'brand': quote.brand,
                'quantity': quote.quantity,
                'price': quote.price,
                'my_price': quote.my_price,
                'delivery_days': quote.delivery_days,
                'inquiry_date': quote.inquiry_date.isoformat(),
                'supplier_name': quote.supplier.name,
                'supplier_contact': quote.supplier.contact,
                'customer_name': quote.inquiry.customer.name if quote.inquiry.customer else '',
                'has_orders': len(quote.orders) > 0  # 是否有关联订单
            } for quote in quotes.items],
            'total': quotes.total,
            'pages': quotes.pages,
            'current_page': quotes.page,
            'has_next': quotes.has_next,
            'has_prev': quotes.has_prev
        })

    except Exception as e:
        print(f"Error getting supplier quotes: {str(e)}")
        return jsonify({"error": "获取报价列表失败"}), 500