from flask import Blueprint, render_template, request, jsonify, send_file, make_response
from models import db, Customer, Order, Inventory, UserActivityLog
from datetime import datetime
from openpyxl.drawing.image import Image
from io import BytesIO
import logging
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from num2words import num2words
from flask_login import current_user
import json
from flask import redirect, url_for
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 统一状态枚举
VALID_STATUSES = {
    'pending': '待处理',
    'confirmed': '已确认',
    'shipped': '已发货',
    'completed': '已完成',
    'cancelled': '已取消'
}

inventory_bp = Blueprint('inventory', __name__)

@inventory_bp.before_request
def require_login():
    allowed_routes = ['login', 'static']
    if not current_user.is_authenticated and request.endpoint not in allowed_routes:
        return redirect(url_for('auth.login', next=request.url))

@inventory_bp.route('/')
def index():
    return render_template('inventory/index.html')

@inventory_bp.route('/customers')
def get_customers():
    customers = Customer.query.order_by(Customer.name).all()
    return jsonify([{
        'id': customer.id,
        'name': customer.name
    } for customer in customers])

@inventory_bp.route('/orders')
def get_orders():
    customer_id = request.args.get('customer_id', type=int)
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    try:
        # 解析前端发送的本地时间
        start_date_local = datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S')
        end_date_local = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')
        
        logger.info(f"查询时间范围: {start_date_local} - {end_date_local}")
        
        # 从 Order 表查询所有订单，并左连接 Inventory 表
        query = Order.query.outerjoin(Inventory).filter(
            Order.customer_id == customer_id,
            Order.order_date >= start_date_local,
            Order.order_date <= end_date_local
        )
        
        # 获取所有记录
        all_records = query.all()
        
        return jsonify({
            'orders': [{
                'id': record.id,
                'inventory_id': record.inventory[0].id if record.inventory else None,
                'order_date': record.order_date.strftime('%Y-%m-%d %H:%M:%S'),
                'product_name': record.product_name,
                'quantity': record.quantity,
                'arrival_date': record.inventory[0].arrival_date.strftime('%Y-%m-%d') if record.inventory and record.inventory[0].arrival_date else None,
                'shipping_method': record.inventory[0].shipping_method if record.inventory else None,
                'shipping_date': record.inventory[0].shipping_date.strftime('%Y-%m-%d') if record.inventory and record.inventory[0].shipping_date else None,
                'supplier_name': record.supplier_name,
                'serial_number': record.inventory[0].serial_number if record.inventory else None,
                'status': record.status  # 直接使用订单状态
            } for record in all_records],
            'time_range': {
                'start_local': start_date_local.strftime('%Y-%m-%d %H:%M:%S'),
                'end_local': end_date_local.strftime('%Y-%m-%d %H:%M:%S')
            }
        })
    except ValueError as e:
        logger.error(f"日期格式无效: {str(e)}")
        return jsonify({'error': '日期格式无效'}), 400

@inventory_bp.route('/add', methods=['POST'])
def add_inventory():
    data = request.get_json()
    try:
        inventory = Inventory(
            order_id=data['order_id'],
            customer_id=data['customer_id'],
            product_name=data['product_name'],
            quantity=data['quantity'],
            shipping_method=data.get('shipping_method'),
            shipping_date=data.get('shipping_date'),
            supplier_name=data.get('supplier_name'),
            serial_number=data.get('serial_number')
        )
        db.session.add(inventory)
        db.session.commit()
        
        # 添加审计日志
        log = UserActivityLog(
            user_id=current_user.id,
            action_type="新增库存",
            details=json.dumps({
                "inventory_id": inventory.id,
                "product_name": inventory.product_name,
                "quantity": inventory.quantity,
                "shipping_method": inventory.shipping_method
            }),
            ip_address=request.remote_addr
        )
        db.session.add(log)
        db.session.commit()

        return jsonify({'message': '库存添加成功', 'id': inventory.id}), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@inventory_bp.route('/update/<int:inventory_id>', methods=['PUT'])
def update_inventory(inventory_id):
    if not request.is_json:
        return jsonify({'error': '请求必须是JSON格式'}), 400
        
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '无效的JSON数据'}), 400
            
        inventory = Inventory.query.get_or_404(inventory_id)
        
        # 记录更新前的数据
        old_data = {
            "product_name": inventory.product_name,
            "quantity": inventory.quantity,
            "arrival_date": inventory.arrival_date.strftime('%Y-%m-%d') if inventory.arrival_date else None,
            "shipping_method": inventory.shipping_method,
            "shipping_date": inventory.shipping_date.strftime('%Y-%m-%d') if inventory.shipping_date else None,
            "serial_number": inventory.serial_number
        }

        # 验证并更新数据
        if 'shipping_method' in data and data['shipping_method'] not in [None, '', 'sea', 'air']:
            return jsonify({'error': '无效的货运方式'}), 400
            
        # 处理到货时间
        if 'arrival_date' in data:
            try:
                arrival_date = datetime.strptime(data['arrival_date'], '%Y-%m-%d') if data['arrival_date'] else None
                inventory.arrival_date = arrival_date
            except ValueError:
                return jsonify({'error': '无效的到货日期格式，请使用YYYY-MM-DD格式'}), 400
            
        # 处理出货时间
        if 'shipping_date' in data:
            try:
                shipping_date = datetime.strptime(data['shipping_date'], '%Y-%m-%d') if data['shipping_date'] else None
                inventory.shipping_date = shipping_date
            except ValueError:
                return jsonify({'error': '无效的日期格式，请使用YYYY-MM-DD格式'}), 400
        
        # 更新其他字段
        allowed_fields = ['product_name', 'quantity', 'shipping_method', 'serial_number', 'supplier_name']
        for field in allowed_fields:
            if field in data:
                setattr(inventory, field, data[field])
        
        # 添加审计日志
        new_data = {
            "product_name": inventory.product_name,
            "quantity": inventory.quantity,
            "arrival_date": inventory.arrival_date.strftime('%Y-%m-%d') if inventory.arrival_date else None,
            "shipping_method": inventory.shipping_method,
            "shipping_date": inventory.shipping_date.strftime('%Y-%m-%d') if inventory.shipping_date else None,
            "serial_number": inventory.serial_number
        }
        
        # 计算差异
        diff = {}
        for key in new_data:
            if new_data[key] != old_data.get(key):
                diff[key] = {
                    'old': old_data.get(key),
                    'new': new_data[key]
                }

        # 添加审计日志（只记录变化字段）
        log = UserActivityLog(
            user_id=current_user.id,
            action_type="更新库存",
            details=json.dumps({
                "inventory_id": inventory.id,
                "changes": diff
            }, ensure_ascii=False),
            ip_address=request.remote_addr
        )
        
        db.session.add(log)
        db.session.commit()

        return jsonify({
            'message': '更新成功',
            'inventory': {
                'id': inventory.id,
                'product_name': inventory.product_name,
                'quantity': inventory.quantity,
                'arrival_date': inventory.arrival_date.strftime('%Y-%m-%d') if inventory.arrival_date else None,
                'shipping_method': inventory.shipping_method,
                'shipping_date': inventory.shipping_date.strftime('%Y-%m-%d') if inventory.shipping_date else None,
                'serial_number': inventory.serial_number,
                'supplier_name': inventory.supplier_name
            }
        }), 200

    except Exception as e:
        db.session.rollback()
        logger.error(f"更新库存失败: {str(e)}")
        return jsonify({'error': f'更新库存失败: {str(e)}'}), 500

@inventory_bp.route('/download', methods=['GET'])
def download_excel():
    try:
        logger.info(f"接收到的全部参数: {request.args}")
        customer_id = request.args.get('customer_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        exchange_rate = float(request.args.get('exchange_rate'))
        shipping_method = request.args.get('shipping_method')
        
        if not all([customer_id, start_date, end_date, shipping_method]):
            return jsonify({'error': '缺少必要参数'}), 400
            
        # 解析日期字符串为 datetime 对象
        start_date = datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S')
        end_date = datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S')
        
        # 修改查询条件：使用shipping_date而不是order_date进行过滤
        query = Inventory.query.join(Order).filter(
            Inventory.customer_id == customer_id,
            Inventory.shipping_method == shipping_method,
            Inventory.shipping_date >= start_date,
            Inventory.shipping_date <= end_date,
            Inventory.shipping_date.isnot(None)  # 确保有出货时间
        )
        
        # 先获取记录数量
        records_count = query.count()
        logger.info(f"查询到 {records_count} 条记录")
        
        if records_count == 0:
            logger.warning("没有找到符合条件的记录")
            return jsonify({'error': '没有找到符合条件的记录'}), 404
            
        # 获取所有记录并按出货日期排序
        records = query.order_by(Inventory.shipping_date).all()
        
        # 合并相同产品的记录
        merged_records = defaultdict(lambda: {
            'product_name': '',
            'quantity': 0,
            'order': None,  # 保存第一条记录的订单信息
            'shipping_dates': set()  # 用于记录所有出货日期
        })
        
        for record in records:
            key = record.product_name
            if not merged_records[key]['product_name']:
                merged_records[key]['product_name'] = record.product_name
                merged_records[key]['order'] = record.order  # 保存第一条记录的订单信息
            merged_records[key]['quantity'] += record.quantity
            if record.shipping_date:
                merged_records[key]['shipping_dates'].add(record.shipping_date.strftime('%Y-%m-%d'))
        
        # 转换为列表
        merged_records_list = []
        for record_info in merged_records.values():
            # 创建一个新的"虚拟"记录对象
            class MergedRecord:
                def __init__(self, product_name, quantity, order, shipping_dates):
                    self.product_name = product_name
                    self.quantity = quantity
                    self.order = order
                    self.shipping_dates = shipping_dates
            
            merged_record = MergedRecord(
                product_name=record_info['product_name'],
                quantity=record_info['quantity'],
                order=record_info['order'],
                shipping_dates=', '.join(sorted(record_info['shipping_dates']))
            )
            merged_records_list.append(merged_record)

        # 获取客户信息
        customer = Customer.query.get(customer_id)
        customer_info = {
            'name': customer.name if customer else '',
            'address': customer.address if customer else '',
            'tel': customer.phone if customer else '',
            'attn': customer.contact if customer else ''
        }
        
        # 使用create_invoice_excel函数生成Excel，传入合并后的记录
        wb = create_invoice_excel(
            records=merged_records_list,
            exchange_rate=exchange_rate,
            customer_info=customer_info
        )
        
        # 保存到内存中
        output = BytesIO()
        wb.save(output)
        output.seek(0)
        
        # 设置文件名格式：{货运方式}-{出货时间}
        shipping_type = '海运' if shipping_method == 'sea' else '空运'
        filename = f'{shipping_type}-{start_date.strftime("%Y-%m-%d")}.xlsx'

        # 添加审计日志
        log = UserActivityLog(
            user_id=current_user.id,
            action_type="下载出货记录",
            details=json.dumps({
                "类型": shipping_type,
                "时间范围": {
                    "开始时间": start_date.strftime('%Y-%m-%d %H:%M:%S'),
                    "结束时间": end_date.strftime('%Y-%m-%d %H:%M:%S')
                },
                "客户ID": customer_id,
                "文件名": filename,
                "汇率": exchange_rate,
                "合并记录数": len(merged_records_list)
            }, ensure_ascii=False),
            ip_address=request.remote_addr
        )
        db.session.add(log)
        db.session.commit()
        
        logger.info(f"生成Excel文件: {filename}")
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        logger.error(f'下载出货记录失败: {str(e)}')
        return jsonify({'error': str(e)}), 500

def create_invoice_excel(records, exchange_rate, customer_info=None):
    total_amount_usd = 0  # Initialize total amount
    wb = Workbook()
    ws = wb.active
    ws.title = 'Shipment Invoice'
    
    # 设置默认行高
    ws.sheet_format.defaultRowHeight = 15.0
    
    # 设置列宽
    ws.column_dimensions['A'].width = 8   # ITEM NO
    ws.column_dimensions['B'].width = 50  # DESCRIPTION
    ws.column_dimensions['C'].width = 15  # QTY
    ws.column_dimensions['D'].width = 15  # Exchange rate
    ws.column_dimensions['E'].width = 15  # PRICE(RMB)
    ws.column_dimensions['F'].width = 15  # PRICE(USD)
    ws.column_dimensions['G'].width = 28  # AMOUNT USD
    ws.column_dimensions['H'].width = 15  # Remark

    # 公司抬头
    ws.merge_cells('A1:H1')
    company_cell = ws['A1']
    company_cell.value = 'SHENZHEN BEIKOI TECHNOLOGY COMPANY LIMITED'
    company_cell.font = Font(name='Times New Roman', size=14, bold=True)
    company_cell.alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[1].height = 18

    # 空行
    ws.row_dimensions[2].height = 16
    ws.row_dimensions[3].height = 16

    # Proforma Invoice
    ws.merge_cells('A4:H4')
    title_cell = ws['A4']
    title_cell.value = 'Proforma Invoice'
    title_cell.font = Font(name='Times New Roman', size=16, bold=True)
    title_cell.alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[4].height = 20

    # 客户信息区域
    customer_info = customer_info or {
        'name': '',
        'address': '',
        'tel': '',
        'fax': '',
        'attn': ''
    }
    
    current_date = datetime.now()
    
    # Messers & Invoice NO
    ws.merge_cells('A5:A6')
    ws['A5'] = 'Messers:'
    ws.merge_cells('B5:B6')
    ws['B5'] = customer_info.get('name', '')
    ws['B5'].font = Font(name='Times New Roman', size=12)
    ws['B5'].alignment = Alignment(horizontal='left', vertical='center')
    ws['F5'] = 'Invoice NO:'
    ws['G5'] = 'SE' + current_date.strftime('%Y%m%d')
    ws['G5'].font = Font(name='Times New Roman', size=12)
    ws.row_dimensions[5].height = 16

    # Date
    ws['F6'] = 'Date:'
    ws['G6'] = current_date.strftime('%Y.%m.%d')
    ws['G6'].font = Font(name='Times New Roman', size=12)
    ws.row_dimensions[6].height = 16

    # Address
    ws.merge_cells('A7:A8')
    ws['A7'] = 'Address:'
    ws.merge_cells('B7:B8')
    ws['B7'] = customer_info.get('address', '')
    ws['B7'].font = Font(name='Times New Roman', size=12)
    ws['B7'].alignment = Alignment(horizontal='left', wrap_text=True, vertical='top')
    ws['F8'] = 'From:'
    ws['G8'] = 'Shenzhen,China'
    ws['G8'].font = Font(name='Times New Roman', size=12)
    ws.row_dimensions[8].height = 16

    # Tel & To
    ws['A9'] = 'TEL:'
    ws['B9'] = customer_info.get('tel', '')
    ws['B9'].font = Font(name='Times New Roman', size=12)
    ws['F9'] = 'To:'
    ws['G9'] = 'KOREA'
    ws['G9'].font = Font(name='Times New Roman', size=12)
    ws.row_dimensions[9].height = 16

    # FAX & PO
    ws['A10'] = 'FAX:'
    ws['B10'] = customer_info.get('fax', '')
    ws['F10'] = 'PO:'
    ws['G10'] = 'N/A'
    ws['G10'].font = Font(name='Times New Roman', size=12)
    ws.row_dimensions[10].height = 16

    # ATTN
    ws['A11'] = 'ATTN:'
    ws['A11'].font = Font(name='Times New Roman', size=12)
    ws['B11'] = customer_info.get('attn', '')
    ws['B11'].font = Font(name='Times New Roman', size=12)
    ws.row_dimensions[11].height = 16

    # 设置客户信息区域字体
    for row in range(5, 12):
        for col in ['A', 'F']:
            cell = ws[f'{col}{row}']
            cell.font = Font(name='Times New Roman', size=12)
            cell.alignment = Alignment(horizontal='left', vertical='center')

    # 表格头部
    headers = ['ITEM', 'DESCRIPTION', 'QTY', '汇率', '单价(RMB)', 'PRICE(USD)', 'AMOUNT', 'Remark']
    header_row = 12
    header_fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=header_row, column=col, value=header)
        cell.font = Font(name='Times New Roman', size=12, bold=True)
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        cell.fill = header_fill
    ws.row_dimensions[header_row].height = 16
    
    # 数据行
    for idx, record in enumerate(records, 1):
        row = idx + header_row
        unit_price_rmb = getattr(record.order, 'my_price', 0) or 0
        unit_price_usd = unit_price_rmb / exchange_rate
        amount_usd = unit_price_usd * record.quantity
        total_amount_usd += amount_usd

        # 设置行高
        ws.row_dimensions[row].height = 16

        # 写入数据
        cells = [
            (1, idx, 'center'),  # ITEM
            (2, record.product_name + '  SEMICONDUCTOR DEVICES  MANUFACTURING', 'center'),  # DESCRIPTION
            (3, record.quantity, 'center'),  # QTY
            (4, exchange_rate, 'center'),  # Exchange rate
            (5, unit_price_rmb, 'center'),  # PRICE(RMB)
            (6, f"{unit_price_usd:.2f}", 'center'),  # PRICE(USD)
            (7, f"{amount_usd:.2f}", 'center'),  # AMOUNT
            (8, record.shipping_dates if hasattr(record, 'shipping_dates') else '', 'center')  # Remark (出货日期)
        ]

        for col, value, align in cells:
            cell = ws.cell(row=row, column=col)
            cell.value = value
            cell.font = Font(name='Times New Roman', size=12, bold=True)
            cell.alignment = Alignment(horizontal=align, vertical='center', wrap_text=True)
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

    # 合计行
    total_row = len(records) + header_row + 1
    ws.row_dimensions[total_row].height = 16

    # Total文本
    total_cell = ws.cell(row=total_row, column=1)
    total_cell.value = 'Total'
    total_cell.font = Font(name='Times New Roman', size=12, bold=True)
    total_cell.alignment = Alignment(horizontal='center', vertical='center')

    # 合计数量
    qty_total_cell = ws.cell(row=total_row, column=3)
    qty_total_cell.value = sum(record.quantity for record in records)
    qty_total_cell.font = Font(name='Times New Roman', size=12, bold=True)
    qty_total_cell.alignment = Alignment(horizontal='center', vertical='center')

    # 合计金额
    amount_total_cell = ws.cell(row=total_row, column=7)
    amount_total_cell.value = f"${total_amount_usd:.2f}"
    amount_total_cell.font = Font(name='Times New Roman', size=12, bold=True, underline='single')
    amount_total_cell.alignment = Alignment(horizontal='center', vertical='center')

    # 为合计行添加边框和设置其他单元格格式
    for col in range(1, 9):
        cell = ws.cell(row=total_row, column=col)
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        # 设置Exchange rate单元格格式
        if col == 4:
            cell.value = exchange_rate
            cell.font = Font(name='Times New Roman', size=12, bold=True)
            cell.alignment = Alignment(horizontal='center', vertical='center')

    # 金额大写
    words_row = total_row + 1
    ws.merge_cells(f'A{words_row}:H{words_row}')
    decimal_part = str(round((total_amount_usd % 1) * 100)).zfill(2)
    decimal_words = ' '.join(num2words(int(d)).upper() for d in decimal_part)
    ws[f'A{words_row}'] = f"TOTAL SAY US DOLLAR {num2words(int(total_amount_usd)).upper()} POINT {decimal_words} ONLY***"
    ws[f'A{words_row}'].font = Font(name='Times New Roman', size=14, bold=True)
    ws[f'A{words_row}'].alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[words_row].height = 16

    # 空行
    ws.row_dimensions[words_row + 1].height = 16
    ws.row_dimensions[words_row + 2].height = 16

    # 付款信息
    payment_row = words_row + 3
    payment_info = [
        '1. Payment Term: 100% payment',
        "2. Bank's information:",
        "    Account Holder's Name: Shenzhen Beikoi Technology Company Limited",
        "    Account Number: **********",
        "    Bank's Name: DBS Bank (Hong Kong) Limited",
        "    Bank's Address: 11th Floor, The Center, 99 Queen's Road Central, Central, Hong Kong",
        "    SWIFT Code: DHBKHKHH (DHBKHKHHXXX * If 11 characters are required)",
        "    Bank Code: 016",
        "Branch Code: 478 * If paying from Hong Kong banks"
    ]
    cur_row = payment_row
    for i, info in enumerate(payment_info):
        row = payment_row + i
        cell = ws[f'A{row}']
        cell.value = info
        
        # 为Bank's information添加下划线
        if i == 1:  # Bank's information行
            cell.font = Font(name='Times New Roman', size=12, underline='single', bold=True)
        else:
            cell.font = Font(name='Times New Roman', size=12, bold=True)
            
        cell.alignment = Alignment(horizontal='left', vertical='center')
        ws.row_dimensions[row].height = 16
        
        # 前两行合并单元格
        if i < 2:
            ws.merge_cells(f'A{row}:H{row}')
        cur_row = row
    company_row = cur_row + 6
    ws.merge_cells(f'A{company_row}:H{company_row}')
    ws[f'A{company_row}'] = 'XT-SHENZHEN BEIKOI TECHNOLOGY COMPANY LIMITED'
    company_cell = ws.cell(row=company_row, column=1)
    company_cell.font = Font(name='Times New Roman', size=12, bold=True)
    company_cell.alignment = Alignment(horizontal='center')
    ws.row_dimensions[company_row].height = 16
    logo = Image('sign.png')
    logo.width = 100
    logo.height = 50
    ws.add_image(logo, f'C{company_row - 3}')
    return wb 