from flask import Blueprint, jsonify
from utils.forex.rates import get_forex_rate
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

forex_bp = Blueprint('forex', __name__)

@forex_bp.route('/rate')
def get_rate():
    try:
        rate, message = get_forex_rate()
        return jsonify({
            'rate': rate,
            'timestamp': message
        })
    except Exception as e:
        logger.error(f"获取汇率失败: {str(e)}")
        return jsonify({'error': '获取汇率失败'}), 500 