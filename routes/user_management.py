from flask import Blueprint, render_template, request, jsonify, redirect, url_for
from flask_login import login_required, current_user
from models import db, User, UserActivityLog
from datetime import datetime


user_management_bp = Blueprint('user_management', __name__)


@user_management_bp.before_request
def require_login():
    allowed_routes = ['login', 'static']
    if not current_user.is_authenticated and request.endpoint not in allowed_routes:
        return redirect(url_for('auth.login', next=request.url))


@user_management_bp.route('/')
def index():
    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403
    return render_template('user_management/index.html')

@user_management_bp.route('/users', methods=['GET'])
def get_users():
    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403
        
    users = User.query.all()
    return jsonify([{
        'id': u.id,
        'username': u.username,
        'is_admin': u.is_admin,
        'created_at': u.created_at.isoformat() if u.created_at else None
    } for u in users])

@user_management_bp.route('/users', methods=['POST'])
@login_required
def create_user():
    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403

    data = request.json
    if not data or 'username' not in data or 'password' not in data:
        return jsonify({'error': '需要用户名和密码'}), 400

    if User.query.filter_by(username=data['username']).first():
        return jsonify({'error': '用户名已存在'}), 400

    user = User(
        username=data['username'],
        is_admin=data.get('is_admin', False),
        created_at=datetime.now()
    )
    user.set_password(data['password'])
    
    try:
        db.session.add(user)
        db.session.commit()
        
        # 添加审计日志
        log = UserActivityLog(
            user_id=current_user.id,
            action_type="创建用户",
            details=f"新用户: {user.username} (管理员: {user.is_admin})",
            ip_address=request.remote_addr
        )
        db.session.add(log)
        db.session.commit()
        
        return jsonify({
            'id': user.id,
            'username': user.username,
            'is_admin': user.is_admin,
            'created_at': user.created_at.isoformat()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'创建用户失败: {str(e)}'}), 500

@user_management_bp.route('/users/<int:user_id>', methods=['DELETE'])
@login_required
def delete_user(user_id):
    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403
        
    if current_user.id == user_id:
        return jsonify({'error': '不能删除当前登录用户'}), 400
        
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': '用户不存在'}), 404
        
    try:
        db.session.delete(user)
        db.session.commit()
        return jsonify({'message': '用户已删除'}), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'删除用户失败: {str(e)}'}), 500

@user_management_bp.route('/users/<int:user_id>/change-password', methods=['PUT'])
@login_required
def change_user_password(user_id):
    if not current_user.is_admin:
        return jsonify({'error': '权限不足'}), 403
        
    data = request.get_json()
    if not data or 'new_password' not in data:
        return jsonify({'error': '需要新密码'}), 400
        
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': '用户不存在'}), 404
        
    try:
        user.set_password(data['new_password'])
        
        # 添加审计日志
        log = UserActivityLog(
            user_id=current_user.id,
            action_type="修改密码",
            details=f"修改用户 {user.username} 的密码",
            ip_address=request.remote_addr
        )
        db.session.add(log)
        db.session.commit()
        
        return jsonify({'message': '密码修改成功'}), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'修改密码失败: {str(e)}'}), 500 