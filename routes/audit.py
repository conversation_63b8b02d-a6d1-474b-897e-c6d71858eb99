from flask import Blueprint, render_template, jsonify, request, send_file
from flask_login import login_required, current_user
from models import db, UserActivityLog, User
from datetime import datetime
from openpyxl import Workbook
from io import BytesIO

audit_bp = Blueprint('audit', __name__)

@audit_bp.route('/')
@login_required
def index():
    if not current_user.is_admin:
        return "Forbidden", 403
    return render_template('audit/index.html')

@audit_bp.route('/filter-options')
@login_required
def get_filter_options():
    if not current_user.is_admin:
        return jsonify([]), 403
    
    # 获取所有用户
    users = User.query.all()
    
    # 获取所有操作类型（去重）
    action_types = db.session.query(UserActivityLog.action_type.distinct()).all()
    
    return jsonify({
        'users': [{
            'id': user.id,
            'username': user.username
        } for user in users],
        'action_types': [action_type[0] for action_type in action_types]
    })

@audit_bp.route('/logs')
@login_required
def get_logs():
    if not current_user.is_admin:
        return jsonify([]), 403
    
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 100, type=int)
    username = request.args.get('username')
    action_type = request.args.get('action_type')
    
    # 构建查询
    query = UserActivityLog.query
    
    # 应用筛选条件
    if username:
        query = query.join(User).filter(User.username == username)
    if action_type:
        query = query.filter(UserActivityLog.action_type == action_type)
    
    # 排序并分页
    logs = query.order_by(
        UserActivityLog.timestamp.desc()
    ).paginate(page=page, per_page=per_page)
    
    return jsonify({
        'total': logs.total,
        'pages': logs.pages,
        'current_page': logs.page,
        'logs': [{
            'timestamp': log.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'username': log.user.username,
            'action_type': log.action_type,
            'details': log.details,
            'ip': log.ip_address
        } for log in logs.items]
    })

@audit_bp.route('/export')
@login_required
def export_logs():
    if not current_user.is_admin:
        return "Forbidden", 403
        
    # 创建Excel工作簿
    wb = Workbook()
    ws = wb.active
    ws.title = "操作日志"
    
    # 设置表头
    headers = ['时间', '用户', '操作类型', '详情', 'IP地址']
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header)
    
    # 获取所有日志记录
    logs = UserActivityLog.query.order_by(UserActivityLog.timestamp.desc()).all()
    
    # 写入数据
    for row, log in enumerate(logs, 2):
        ws.cell(row=row, column=1, value=log.timestamp.strftime('%Y-%m-%d %H:%M:%S'))
        ws.cell(row=row, column=2, value=log.user.username)
        ws.cell(row=row, column=3, value=log.action_type)
        ws.cell(row=row, column=4, value=log.details)
        ws.cell(row=row, column=5, value=log.ip_address)
    
    # 调整列宽
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = (max_length + 2)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # 保存到内存
    excel_file = BytesIO()
    wb.save(excel_file)
    excel_file.seek(0)
    
    return send_file(
        excel_file,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=f'操作日志_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    ) 