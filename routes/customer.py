from flask import Blueprint, render_template, request, jsonify, redirect, url_for
from models import db, Customer, UserActivityLog
import json
from flask_login import current_user

customer_bp = Blueprint('customer', __name__)

# 国家列表（中英文对照）
COUNTRIES = {
    'USA': '美国',
    'JAPAN': '日本',
    'KOREA': '韩国',
}

@customer_bp.route('/')
def index():
    return render_template('customer/index.html')


@customer_bp.before_request
def require_login():
    allowed_routes = ['login', 'static']
    if not current_user.is_authenticated and request.endpoint not in allowed_routes:
        return redirect(url_for('auth.login', next=request.url))


@customer_bp.route('/list')
def get_customers():
    customers = Customer.query.order_by(Customer.name).all()
    return jsonify([{
        'id': customer.id,
        'name': customer.name,
        'address': customer.address,
        'phone': customer.phone,
        'contact': customer.contact,
        'country': customer.country,
        'country_name': COUNTRIES.get(customer.country, customer.country)
    } for customer in customers])

@customer_bp.route('/countries')
def get_countries():
    return jsonify([{
        'code': code,
        'name': name
    } for code, name in COUNTRIES.items()])

@customer_bp.route('/add', methods=['POST'])
def add_customer():
    data = request.get_json()
    
    # 验证所有必填字段
    required_fields = {
        'name': '客户名称',
        'address': '地址',
        'phone': '电话',
        'contact': '联系人',
        'country': '国家'
    }
    
    for field, field_name in required_fields.items():
        if not data.get(field):
            return jsonify({'error': f'{field_name}不能为空'}), 400
        
    # 创建新客户
    customer = Customer(
        name=data['name'],
        address=data['address'],
        phone=data['phone'],
        contact=data['contact'],
        country=data['country']
    )
    
    try:
        db.session.add(customer)
        db.session.commit()
        
        # 添加审计记录
        log = UserActivityLog(
            user_id=current_user.id,
            action_type="新增客户",
            details=json.dumps({
                "customer_id": customer.id,
                "name": customer.name,
                "country": customer.country
            }),
            ip_address=request.remote_addr
        )
        db.session.add(log)
        db.session.commit()
        
        return jsonify({
            'id': customer.id,
            'name': customer.name,
            'address': customer.address,
            'phone': customer.phone,
            'contact': customer.contact,
            'country': customer.country,
            'country_name': COUNTRIES.get(customer.country, customer.country)
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@customer_bp.route('/<int:customer_id>', methods=['PUT'])
def update_customer(customer_id):
    customer = Customer.query.get_or_404(customer_id)
    data = request.get_json()
    
    # 验证所有必填字段
    required_fields = {
        'name': '客户名称',
        'address': '地址',
        'phone': '电话',
        'contact': '联系人',
        'country': '国家'
    }
    
    for field, field_name in required_fields.items():
        if field in data and not data[field]:
            return jsonify({'error': f'{field_name}不能为空'}), 400
        
    try:
        if 'name' in data:
            customer.name = data['name']
        if 'address' in data:
            customer.address = data['address']
        if 'phone' in data:
            customer.phone = data['phone']
        if 'contact' in data:
            customer.contact = data['contact']
        if 'country' in data:
            customer.country = data['country']
            
        db.session.commit()
        
        return jsonify({
            'id': customer.id,
            'name': customer.name,
            'address': customer.address,
            'phone': customer.phone,
            'contact': customer.contact,
            'country': customer.country,
            'country_name': COUNTRIES.get(customer.country, customer.country)
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@customer_bp.route('/<int:customer_id>', methods=['DELETE'])
def delete_customer(customer_id):
    customer = Customer.query.get_or_404(customer_id)
    
    try:
        db.session.delete(customer)
        db.session.commit()
        return jsonify({'message': '客户已删除'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500 